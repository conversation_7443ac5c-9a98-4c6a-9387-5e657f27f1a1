import pygame
import random
import math
from settings import *

class Food:
    def __init__(self):
        """初始化食物"""
        self.position = (0, 0)
        self.color = RED
        self.glow_color = (255, 100, 100)
        self.glow_size = 0
        self.animation_counter = 0
        self.randomize_position()

    def randomize_position(self):
        """随机生成食物位置"""
        self.position = (
            random.randint(0, GRID_WIDTH - 1),
            random.randint(0, GRID_HEIGHT - 1)
        )

    def draw(self, screen):
        """绘制食物"""
        # 计算游戏区域的起始位置
        game_start_x = BORDER_PADDING_X + BORDER_WIDTH
        game_start_y = BORDER_PADDING_Y + BORDER_WIDTH
        
        # 计算实际的屏幕位置
        screen_x = game_start_x + self.position[0] * CELL_SIZE
        screen_y = game_start_y + self.position[1] * CELL_SIZE
        
        # 绘制食物
        rect = pygame.Rect(screen_x, screen_y, CELL_SIZE, CELL_SIZE)
        pygame.draw.rect(screen, FOOD_COLOR, rect)
        pygame.draw.rect(screen, BORDER_HIGHLIGHT, rect, 2)  # 高亮边框

    def update(self):
        """更新食物位置"""
        self.randomize_position()

    def get_position(self):
        """获取食物位置"""
        return self.position 