# Python Snake Game

这是一个使用 Python 和 Pygame 开发的经典贪吃蛇游戏。

## 游戏特点

- 简单直观的操作方式
- 清晰的游戏界面
- 实时分数显示
- 食物随机生成
- 碰撞检测（墙壁和自身）
- 游戏结束提示

## 如何运行

1. 确保你已经安装了 Python 3.x
2. 安装必要的依赖：
   ```bash
   pip install -r requirements.txt
   ```
3. 运行游戏：
   ```bash
   python main.py
   ```

## 游戏控制

- 使用方向键（↑ ↓ ← →）控制蛇的移动
- ESC 键暂停游戏
- 空格键重新开始游戏

## 项目结构

```
snake-game/
│
├── main.py              # 游戏主入口
├── game.py             # 游戏核心逻辑
├── snake.py            # 蛇的类定义
├── food.py             # 食物类定义
├── settings.py         # 游戏配置
└── requirements.txt    # 项目依赖
```

## 开发计划

- [x] 基础游戏功能
- [ ] 添加音效
- [ ] 添加不同难度级别
- [ ] 保存最高分数
- [ ] 添加特殊食物效果

## 技术栈

- Python 3.x
- Pygame 2.x 