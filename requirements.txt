altgraph==0.17.4
anyio==4.5.2
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==3.0.0
async-lru==2.0.4
attrs==24.3.0
babel==2.16.0
backcall==0.2.0
beautifulsoup4==4.12.3
bleach==6.1.0
blinker @ file:///C:/b/abs_d9y2dm7cw2/croot/blinker_1696539752170/work
Bottleneck @ file:///C:/b/abs_f05kqh7yvj/croot/bottleneck_1707864273291/work
certifi==2024.12.14
cffi==1.17.1
charset-normalizer==3.4.1
click @ file:///C:/b/abs_f9ihnt72pu/croot/click_1698129847492/work
colorama @ file:///C:/b/abs_a9ozq0l032/croot/colorama_1672387194846/work
comm==0.2.2
contourpy==1.1.1
cycler==0.12.1
debugpy==1.8.12
decorator==5.1.1
defusedxml==0.7.1
dominate==2.9.1
exceptiongroup==1.2.2
executing==2.1.0
fastjsonschema==2.21.1
Flask @ file:///home/<USER>/feedstock_root/build_artifacts/flask_1696349106907/work
Flask-Bootstrap4==4.0.2
Flask-WTF==1.2.1
fonttools==4.55.3
fqdn==1.5.1
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
idna==3.10
importlib-metadata @ file:///C:/b/abs_c1egths604/croot/importlib_metadata-suite_1704813568388/work
importlib_resources==6.4.5
infi.systray==0.1.12
iniconfig==2.0.0
ipykernel==6.29.5
ipython==8.12.3
isoduration==20.11.0
itsdangerous @ file:///C:/b/abs_c4vwgdr5yn/croot/itsdangerous_1716533399914/work
jedi==0.19.2
Jinja2==3.1.2
json5==0.10.0
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.14.2
jupyter_server_terminals==0.5.3
jupyterlab==4.3.4
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
kiwisolver==1.4.7
MarkupSafe @ file:///C:/b/abs_ecfdqh67b_/croot/markupsafe_1704206030535/work
matplotlib==3.7.5
matplotlib-inline==0.1.7
mistune==3.1.0
mkl-fft @ file:///C:/b/abs_19i1y8ykas/croot/mkl_fft_1695058226480/work
mkl-random @ file:///C:/b/abs_edwkj1_o69/croot/mkl_random_1695059866750/work
mkl-service==2.4.0
mypy==1.8.0
mypy-extensions==1.0.0
nbclient==0.10.1
nbconvert==7.16.5
nbformat==5.10.4
nest-asyncio==1.6.0
notebook==7.3.2
notebook_shim==0.2.4
numexpr @ file:///C:/b/abs_afm0oewmmt/croot/numexpr_1683221839116/work
numpy @ file:///C:/Users/<USER>/mkl/numpy_and_numpy_base_1682982345978/work
opencv-python==4.11.0.86
overrides==7.7.0
packaging==24.2
pandas @ file:///C:/miniconda3/conda-bld/pandas_1692299636855/work
pandocfilters==1.5.1
parso==0.8.4
pefile==2023.2.7
pickleshare==0.7.5
pillow==10.2.0
pkgutil_resolve_name==1.3.10
platformdirs==4.3.6
plotly==5.24.1
pluggy==1.5.0
plyer==2.1.0
prometheus_client==0.21.1
prompt_toolkit==3.0.48
psutil==6.1.1
pure_eval==0.2.3
pycparser==2.22
pygame==2.5.2
Pygments==2.19.1
pyinstaller==6.3.0
pyinstaller-hooks-contrib==2025.1
pyparsing==3.1.4
pypiwin32==223
PyQt6==6.7.1
PyQt6-Qt6==6.7.3
PyQt6_sip==13.8.0
pystray==0.19.5
pytest==7.4.3
python-dateutil @ file:///C:/b/abs_3au_koqnbs/croot/python-dateutil_1716495777160/work
python-dotenv==1.0.0
python-json-logger==3.2.1
pytz @ file:///C:/b/abs_6ap4tsz1ox/croot/pytz_1713974360290/work
pywin32==306
pywin32-ctypes==0.2.3
pywinpty==2.0.14
PyYAML==6.0.1
pyzmq==26.2.0
referencing==0.35.1
requests==2.31.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rpds-py==0.20.1
ruff==0.1.9
schedule==1.2.0
seaborn==0.13.2
Send2Trash==1.8.3
six @ file:///tmp/build/80754af9/six_1644875935023/work
sniffio==1.3.1
soupsieve==2.6
stack-data==0.6.3
tenacity==9.0.0
terminado==0.18.1
tinycss2==1.2.1
tkcalendar==1.6.1
tomli==2.2.1
tornado==6.4.2
traitlets==5.14.3
types-python-dateutil==2.9.0.20241206
types-PyYAML==*********
types-requests==*********
typing_extensions==4.12.2
tzdata @ file:///croot/python-tzdata_1690578112552/work
uri-template==1.3.0
urllib3==2.2.3
visitor==0.1.3
wcwidth==0.2.13
webcolors==24.8.0
webencodings==0.5.1
websocket-client==1.8.0
Werkzeug @ file:///C:/b/abs_8bittcw9jr/croot/werkzeug_1716533366070/work
win10toast==0.9
winotify==1.1.0
WTForms==3.1.2
zipp @ file:///C:/b/abs_021kqswk3k/croot/zipp_1729012371206/work
