from flask import Flask, render_template, request, redirect, url_for, flash, send_from_directory, jsonify
from werkzeug.utils import secure_filename
import os
from src.core.album import Album
from src.core.image import ImageProcessor

# 获取项目根目录的绝对路径
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

app = Flask(__name__)
app.secret_key = 'your-secret-key'  # 用于Flash消息
app.config['UPLOAD_FOLDER'] = os.path.join(BASE_DIR, 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 限制上传文件大小为16MB

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """首页：显示所有相册"""
    albums_dir = os.path.join(BASE_DIR, 'albums')
    albums = []
    if os.path.exists(albums_dir):
        for album_name in os.listdir(albums_dir):
            album_path = os.path.join(albums_dir, album_name)
            if os.path.isdir(album_path):
                album = Album(album_name)
                albums.append({
                    'name': album_name,
                    'image_count': len(album.images),
                    'cover_image': album.images[0]['path'] if album.images else None
                })
    return render_template('index.html', albums=albums)

@app.route('/album/<name>')
def view_album(name):
    """查看特定相册"""
    album = Album(name)
    layout = request.args.get('layout', 'grid')
    album.set_layout(layout)
    return render_template('album.html', album=album, layout=layout)

@app.route('/album/create', methods=['GET', 'POST'])
def create_album():
    """创建新相册"""
    if request.method == 'POST':
        album_name = request.form.get('name')
        if album_name:
            album = Album(album_name)
            flash('相册创建成功！')
            return redirect(url_for('view_album', name=album_name))
    return render_template('create_album.html')

@app.route('/album/<name>/upload', methods=['POST'])
def upload_image(name):
    """上传图片到相册"""
    if 'files[]' not in request.files:
        return '没有选择文件', 400
    
    files = request.files.getlist('files[]')
    if not files:
        return '没有选择文件', 400
    
    album = Album(name)
    filter_name = request.form.get('filter')
    tags = request.form.get('tags', '').split(',')
    tags = [tag.strip() for tag in tags if tag.strip()]
    
    success_count = 0
    error_messages = []
    
    for file in files:
        if file.filename == '':
            continue
            
        if file and allowed_file(file.filename):
            try:
                filename = secure_filename(file.filename)
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                
                # 如果文件已存在，添加数字后缀
                counter = 1
                while os.path.exists(filepath):
                    name, ext = os.path.splitext(filename)
                    filepath = os.path.join(app.config['UPLOAD_FOLDER'], f"{name}_{counter}{ext}")
                    counter += 1
                
                file.save(filepath)
                
                # 处理图片
                processor = ImageProcessor(filepath)
                processor.resize((800, 600))  # 调整大小
                
                # 应用滤镜（如果指定）
                if filter_name:
                    processor.apply_filter(filter_name)
                
                # 保存处理后的图片
                processor.save(filepath)
                
                # 添加到相册
                if album.add_image(filepath, tags=tags):
                    success_count += 1
                else:
                    error_messages.append(f'添加图片到相册失败: {file.filename}')
                    if os.path.exists(filepath):
                        os.remove(filepath)
                        
            except Exception as e:
                error_messages.append(f'处理图片失败 {file.filename}: {str(e)}')
                if os.path.exists(filepath):
                    os.remove(filepath)
        else:
            error_messages.append(f'不支持的文件类型: {file.filename}')
    
    if success_count > 0:
        message = f'成功上传 {success_count} 张图片'
        if error_messages:
            message += f'，{len(error_messages)} 张图片上传失败'
        return message, 200
    else:
        return '\n'.join(error_messages), 400

@app.route('/album/<name>/delete/<path:image_path>', methods=['POST'])
def delete_image(name, image_path):
    """从相册中删除图片"""
    album = Album(name)
    if album.remove_image(image_path):
        # 同时删除上传目录中的文件
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], os.path.basename(image_path))
        if os.path.exists(filepath):
            os.remove(filepath)
        flash('图片删除成功！')
    else:
        flash('删除图片失败')
    return redirect(url_for('view_album', name=name))

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """提供上传的文件访问"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/album/<name>/sphere')
def view_album_sphere(name):
    """3D球形展示相册"""
    album = Album(name)
    return render_template('sphere.html', album=album)

@app.route('/sphere-gallery')
def sphere_gallery():
    """3D球形展示页面"""
    # 获取所有相册中的图片
    albums_dir = os.path.join(BASE_DIR, 'albums')
    all_images = []
    
    if os.path.exists(albums_dir):
        for album_name in os.listdir(albums_dir):
            album_path = os.path.join(albums_dir, album_name)
            if os.path.isdir(album_path):
                album = Album(album_name)
                all_images.extend(album.images)
    
    return render_template('sphere_gallery.html', images=all_images)

if __name__ == '__main__':
    app.run(debug=True) 