# 多变形相册 (Dynamic Photo Album)

这是一个基于Python的多变形相册项目，提供丰富的相册展示效果和图片处理功能。

## 功能特点

1. 多种相册展示效果
   - 网格布局展示
   - 瀑布流布局
   - 3D旋转展示
   - 幻灯片放映
   - 时间轴展示

2. 图片处理功能
   - 自动调整图片大小
   - 添加滤镜效果
   - 图片格式转换
   - 图片压缩

3. 相册管理
   - 创建/删除相册
   - 添加/删除图片
   - 图片分类和标签
   - 图片搜索

## 项目结构

```
photo-album/
├── src/
│   ├── core/           # 核心功能模块
│   │   ├── album.py    # 相册基础类
│   │   ├── image.py    # 图片处理类
│   │   └── storage.py  # 存储管理类
│   ├── layouts/        # 布局展示模块
│   │   ├── grid.py     # 网格布局
│   │   ├── waterfall.py# 瀑布流布局
│   │   ├── rotation.py # 3D旋转
│   │   └── timeline.py # 时间轴布局
│   └── effects/        # 特效模块
│       ├── filters.py  # 滤镜效果
│       └── transitions.py # 转场效果
├── examples/           # 示例代码
├── tests/             # 测试用例
└── requirements.txt   # 项目依赖
```

## 使用方法

1. 安装依赖
```bash
pip install -r requirements.txt
```

2. 基础使用示例
```python
from photo_album import Album

# 创建相册
album = Album("我的相册")

# 添加图片
album.add_image("path/to/image.jpg")

# 设置展示效果
album.set_layout("grid")  # 网格布局
album.set_layout("waterfall")  # 瀑布流布局
album.set_layout("3d")  # 3D旋转
album.set_layout("slideshow")  # 幻灯片
album.set_layout("timeline")  # 时间轴

# 应用滤镜
album.apply_filter("vintage")  # 复古效果
album.apply_filter("black_white")  # 黑白效果

# 导出相册
album.export("output/my_album")
```

## 开发计划

- [x] 项目基础架构搭建
- [ ] 核心功能模块开发
- [ ] 布局展示模块开发
- [ ] 特效模块开发
- [ ] 示例代码编写
- [ ] 单元测试编写
- [ ] 文档完善

## 贡献指南

欢迎提交Issue和Pull Request来帮助改进这个项目。

## 许可证

MIT License 