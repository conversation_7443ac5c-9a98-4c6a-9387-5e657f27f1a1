@charset "utf-8";
@-moz-keyframes number {
	0% {
		transform: translateY(10rem);
		-ms-transform: translateY(10rem);
		-webkit-transform: translateY(10rem);
		-o-transform: translateY(10rem);
		-moz-transform: translateY(10rem);
		opacity: 0;
	}
	25% {
		transform: translateY(7.5rem);
		-ms-transform: translateY(7.5rem);
		-webkit-transform: translateY(7.5rem);
		-o-transform: translateY(7.5rem);
		-moz-transform: translateY(7.5rem);
		opacity: 0.25;
	}
	50% {
		transform: translateY(5rem);
		-ms-transform: translateY(5rem);
		-webkit-transform: translateY(5rem);
		-o-transform: translateY(5rem);
		-moz-transform: translateY(5rem);
		opacity: 0.5;
	}
	75% {
		transform: translateY(2.5rem);
		-ms-transform: translateY(2.5rem);
		-webkit-transform: translateY(2.5rem);
		-o-transform: translateY(2.5rem);
		-moz-transform: translateY(2.5rem);
		opacity: 0.75;
	}
	100% {
		transform: translateY(0rem);
		-ms-transform: translateY(0rem);
		-webkit-transform: translateY(0rem);
		-o-transform: translateY(0rem);
		-moz-transform: translateY(0rem);
		opacity: 1;
	}
}

@-ms-keyframes number {
	0% {
		transform: translateY(10rem);
		-ms-transform: translateY(10rem);
		-webkit-transform: translateY(10rem);
		-o-transform: translateY(10rem);
		-moz-transform: translateY(10rem);
		opacity: 0;
	}
	25% {
		transform: translateY(7.5rem);
		-ms-transform: translateY(7.5rem);
		-webkit-transform: translateY(7.5rem);
		-o-transform: translateY(7.5rem);
		-moz-transform: translateY(7.5rem);
		opacity: 0.25;
	}
	50% {
		transform: translateY(5rem);
		-ms-transform: translateY(5rem);
		-webkit-transform: translateY(5rem);
		-o-transform: translateY(5rem);
		-moz-transform: translateY(5rem);
		opacity: 0.5;
	}
	75% {
		transform: translateY(2.5rem);
		-ms-transform: translateY(2.5rem);
		-webkit-transform: translateY(2.5rem);
		-o-transform: translateY(2.5rem);
		-moz-transform: translateY(2.5rem);
		opacity: 0.75;
	}
	100% {
		transform: translateY(0rem);
		-ms-transform: translateY(0rem);
		-webkit-transform: translateY(0rem);
		-o-transform: translateY(0rem);
		-moz-transform: translateY(0rem);
		opacity: 1;
	}
}

@-webkit-keyframes number {
	0% {
		transform: translateY(10rem);
		-ms-transform: translateY(10rem);
		-webkit-transform: translateY(10rem);
		-o-transform: translateY(10rem);
		-moz-transform: translateY(10rem);
		opacity: 0;
	}
	25% {
		transform: translateY(7.5rem);
		-ms-transform: translateY(7.5rem);
		-webkit-transform: translateY(7.5rem);
		-o-transform: translateY(7.5rem);
		-moz-transform: translateY(7.5rem);
		opacity: 0.25;
	}
	50% {
		transform: translateY(5rem);
		-ms-transform: translateY(5rem);
		-webkit-transform: translateY(5rem);
		-o-transform: translateY(5rem);
		-moz-transform: translateY(5rem);
		opacity: 0.5;
	}
	75% {
		transform: translateY(2.5rem);
		-ms-transform: translateY(2.5rem);
		-webkit-transform: translateY(2.5rem);
		-o-transform: translateY(2.5rem);
		-moz-transform: translateY(2.5rem);
		opacity: 0.75;
	}
	100% {
		transform: translateY(0rem);
		-ms-transform: translateY(0rem);
		-webkit-transform: translateY(0rem);
		-o-transform: translateY(0rem);
		-moz-transform: translateY(0rem);
		opacity: 1;
	}
}

@keyframes number {
	0% {
		transform: translateY(10rem);
		-ms-transform: translateY(10rem);
		-webkit-transform: translateY(10rem);
		-o-transform: translateY(10rem);
		-moz-transform: translateY(10rem);
		opacity: 0;
	}
	25% {
		transform: translateY(7.5rem);
		-ms-transform: translateY(7.5rem);
		-webkit-transform: translateY(7.5rem);
		-o-transform: translateY(7.5rem);
		-moz-transform: translateY(7.5rem);
		opacity: 0.25;
	}
	50% {
		transform: translateY(5rem);
		-ms-transform: translateY(5rem);
		-webkit-transform: translateY(5rem);
		-o-transform: translateY(5rem);
		-moz-transform: translateY(5rem);
		opacity: 0.5;
	}
	75% {
		transform: translateY(2.5rem);
		-ms-transform: translateY(2.5rem);
		-webkit-transform: translateY(2.5rem);
		-o-transform: translateY(2.5rem);
		-moz-transform: translateY(2.5rem);
		opacity: 0.75;
	}
	100% {
		transform: translateY(0rem);
		-ms-transform: translateY(0rem);
		-webkit-transform: translateY(0rem);
		-o-transform: translateY(0rem);
		-moz-transform: translateY(0rem);
		opacity: 1;
	}
}