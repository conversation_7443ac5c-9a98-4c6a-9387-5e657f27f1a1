('C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6',
 'tk',
 ['demos', '*.lib', 'tkConfig.sh'],
 'DATA',
 [('tk\\bgerror.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('tk\\button.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('tk\\choosedir.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tk\\clrpick.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('tk\\comdlg.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('tk\\console.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('tk\\dialog.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('tk\\entry.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('tk\\focus.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tk\\iconlist.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tk\\icons.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('tk\\license.terms',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tk\\listbox.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('tk\\megawidget.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tk\\menu.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('tk\\mkpsenc.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('tk\\msgbox.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('tk\\obsolete.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('tk\\optMenu.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('tk\\palette.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('tk\\safetk.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('tk\\scale.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('tk\\scrlbar.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('tk\\spinbox.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('tk\\tclIndex',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('tk\\tearoff.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tk\\text.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('tk\\tk.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('tk\\tkfbox.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('tk\\unsupported.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tk\\xmfbox.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('tk\\images\\logo.eps',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tk\\images\\README',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'C:\\Users\\<USER>\\miniconda3\\envs\\python-learning\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA')])
