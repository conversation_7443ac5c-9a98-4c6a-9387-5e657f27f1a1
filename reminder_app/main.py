import sys
import datetime
import os
import json
import pickle
import winreg
import math
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                           QHBoxLayout, QPushButton, QLabel, QLineEdit,
                           QMessageBox, QTimeEdit, QRadioButton, QButtonGroup,
                           QSystemTrayIcon, QMenu, QListWidget, QListWidgetItem,
                           QDialog)
from PySide6.QtCore import QTimer, QTime, Qt, QSize, Signal
from PySide6.QtGui import QFont, QIcon, QAction, QCloseEvent, QPixmap, QPainter, QBrush, QPen, QColor, QRadialGradient

def get_app_data_dir():
    """获取应用程序数据目录"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的程序
        app_dir = os.path.dirname(sys.executable)
    else:
        # 如果是开发环境
        app_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 确保目录存在
    if not os.path.exists(app_dir):
        os.makedirs(app_dir)
    
    return app_dir

# 使用math库中的三角函数
def qCos(angle):
    return math.cos(angle)
    
def qSin(angle):
    return math.sin(angle)

class ReminderItem:
    def __init__(self, message, is_interval=True, interval=0, target_time=None):
        self.message = message
        self.is_interval = is_interval
        self.interval = interval  # 毫秒
        self.target_time = target_time  # datetime对象
        self.active = True
        self.last_triggered = None  # 最后一次触发时间
        self.next_trigger = None    # 下一次触发时间
        self.timer_id = None        # 定时器ID
        
    def get_display_text(self):
        if self.is_interval:
            minutes = self.interval // (60 * 1000)
            status = ""
            if self.next_trigger:
                time_left = (self.next_trigger - datetime.datetime.now()).total_seconds()
                if time_left > 0:
                    mins_left = int(time_left // 60)
                    secs_left = int(time_left % 60)
                    status = f" [还剩 {mins_left}分{secs_left}秒]"
            return f"每 {minutes} 分钟: {self.message}{status}"
        else:
            time_str = self.target_time.strftime("%H:%M")
            return f"{time_str}: {self.message}"

    def to_dict(self):
        """将提醒项转换为字典，用于保存数据"""
        data = {
            'message': self.message,
            'is_interval': self.is_interval,
            'interval': self.interval,
            'active': self.active
        }
        
        # 序列化datetime对象
        if self.target_time:
            data['target_time'] = self.target_time.strftime('%Y-%m-%d %H:%M:%S')
        else:
            data['target_time'] = None
            
        if self.last_triggered:
            data['last_triggered'] = self.last_triggered.strftime('%Y-%m-%d %H:%M:%S')
        else:
            data['last_triggered'] = None
            
        if self.next_trigger:
            data['next_trigger'] = self.next_trigger.strftime('%Y-%m-%d %H:%M:%S')
        else:
            data['next_trigger'] = None
            
        return data
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建提醒项，用于加载数据"""
        reminder = cls(
            message=data['message'],
            is_interval=data['is_interval'],
            interval=data['interval']
        )
        
        reminder.active = data['active']
        
        # 反序列化datetime对象
        if data['target_time']:
            reminder.target_time = datetime.datetime.strptime(data['target_time'], '%Y-%m-%d %H:%M:%S')
        
        if data['last_triggered']:
            reminder.last_triggered = datetime.datetime.strptime(data['last_triggered'], '%Y-%m-%d %H:%M:%S')
        
        if data['next_trigger']:
            reminder.next_trigger = datetime.datetime.strptime(data['next_trigger'], '%Y-%m-%d %H:%M:%S')
        
        return reminder

class ReminderDialog(QDialog):
    """自定义提醒对话框，禁用关闭按钮，独立于主窗口"""
    confirmClicked = Signal(object)  # 传递reminder对象的信号
    
    def __init__(self, reminder, parent=None):
        super().__init__(parent)
        self.reminder = reminder
        self.setWindowTitle("提醒")
        self.can_close = False  # 标记是否允许关闭
        
        # 禁用关闭按钮，设置窗口在最前面
        self.setWindowFlags(
            Qt.Dialog | 
            Qt.WindowStaysOnTopHint | 
            Qt.CustomizeWindowHint | 
            Qt.WindowTitleHint
        )
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 添加顶部图标
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("background: transparent;")
        
        # 创建一个简单的闹钟图标
        pixmap = self.create_alarm_icon()
        icon_label.setPixmap(pixmap)
        layout.addWidget(icon_label)
        
        # 添加标题
        title_label = QLabel("时间到了！")
        title_label.setStyleSheet("""
            font-size: 16pt; 
            font-weight: bold; 
            color: #333333;
            margin-bottom: 10px;
            background: transparent;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 添加消息文本
        message_label = QLabel(reminder.message)
        message_label.setStyleSheet("""
            font-size: 14pt; 
            font-weight: bold; 
            color: #5b9cf6;
            margin: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
        """)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)  # 允许文本换行
        layout.addWidget(message_label)
        
        # 添加时间信息
        time_text = datetime.datetime.now().strftime("%H:%M:%S")
        time_label = QLabel(f"当前时间: {time_text}")
        time_label.setStyleSheet("""
            font-size: 10pt; 
            color: #777777;
            background: transparent;
        """)
        time_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(time_label)
        
        # 添加确认按钮
        button_container = QWidget()
        button_container.setStyleSheet("background: transparent;")
        button_layout = QHBoxLayout(button_container)
        
        ok_button = QPushButton("确定")
        ok_button.setStyleSheet("""
            padding: 10px 30px;
            background-color: #5b9cf6;
            color: white;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            font-size: 12pt;
            min-width: 120px;
            min-height: 40px;
        """)
        ok_button.clicked.connect(self.on_confirm)
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addStretch()
        
        layout.addWidget(button_container)
        
        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #4a90e2;
            }
            QPushButton:pressed {
                background-color: #3d7dcf;
            }
        """)
        
        # 设置窗口大小
        self.setMinimumSize(380, 300)
        
    def create_alarm_icon(self):
        """创建闹钟图标"""
        size = 64
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制闹钟底座
        painter.setPen(QPen(QColor("#333333"), 2))
        painter.setBrush(QBrush(QColor("#5b9cf6")))
        painter.drawRoundedRect(size*0.25, size*0.7, size*0.5, size*0.15, 5, 5)
        
        # 绘制闹钟主体
        painter.setBrush(QBrush(QColor("#f0f0f0")))
        painter.drawEllipse(size*0.1, size*0.1, size*0.8, size*0.8)
        
        # 绘制闹钟顶部铃铛
        painter.setBrush(QBrush(QColor("#e54545")))
        painter.drawEllipse(size*0.42, size*0.05, size*0.16, size*0.16)
        
        # 绘制闹钟指针
        painter.setPen(QPen(QColor("#333333"), 2))
        center_x = size / 2
        center_y = size / 2
        
        # 时针（短）
        hour_angle = 45 * 3.14159 / 180  # 1点30分
        hour_x = center_x + size*0.25 * qCos(hour_angle)
        hour_y = center_y + size*0.25 * qSin(hour_angle)
        painter.drawLine(center_x, center_y, hour_x, hour_y)
        
        # 分针（长）
        minute_angle = 260 * 3.14159 / 180  # 8点40分
        minute_x = center_x + size*0.35 * qCos(minute_angle)
        minute_y = center_y + size*0.35 * qSin(minute_angle)
        painter.drawLine(center_x, center_y, minute_x, minute_y)
        
        # 中心点
        painter.setBrush(QBrush(QColor("#333333")))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(center_x-2, center_y-2, 4, 4)
        
        # 绘制闹钟脚
        painter.setPen(QPen(QColor("#333333"), 2))
        painter.drawLine(size*0.3, size*0.85, size*0.2, size*0.95)
        painter.drawLine(size*0.7, size*0.85, size*0.8, size*0.95)
        
        painter.end()
        return pixmap
        
    def on_confirm(self):
        # 允许关闭窗口
        self.can_close = True
        # 发出信号，传递reminder对象
        self.confirmClicked.emit(self.reminder)
        # 使用hide()而不是close()来隐藏窗口
        self.hide()
        
    def closeEvent(self, event):
        # 只有通过确认按钮才允许关闭
        if self.can_close:
            event.accept()
        else:
            event.ignore()

class ReminderApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("提醒助手")
        self.setGeometry(100, 100, 600, 550)  # 增加窗口尺寸，提供更好的布局空间
        
        # 获取应用程序路径，用于设置自启动
        self.app_path = os.path.abspath(sys.argv[0])
        self.app_name = "提醒助手"
        
        # 设置应用图标
        icon_path = os.path.join(get_app_data_dir(), "reminder_icon.png")
        if os.path.exists(icon_path):
            self.app_icon = QIcon(icon_path)
        else:
            self.app_icon = QIcon(self.create_default_icon())
        self.setWindowIcon(self.app_icon)
        
        # 设置应用全局字体
        font = QFont("Microsoft YaHei", 10)
        QApplication.setFont(font)
        
        # 初始化变量
        self.timers = {}  # 用于存储多个定时器
        self.reminders = []  # 存储所有提醒项
        self.active_reminder_dialogs = {}  # 存储当前活动的提醒对话框
        self.data_file = os.path.join(get_app_data_dir(), "reminders_data.json")
        
        # 创建主窗口部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 设置窗口样式 - 更现代化的扁平设计
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f7;
            }
            QWidget {
                background-color: #ffffff;
                border-radius: 8px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
                background: transparent;
            }
            QLineEdit, QTimeEdit {
                padding: 10px;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                background-color: #f9f9f9;
                color: #333333;
                font-size: 11pt;
            }
            QLineEdit:focus, QTimeEdit:focus {
                border-color: #5b9cf6;
                background-color: #ffffff;
            }
            QPushButton {
                padding: 10px 15px;
                background-color: #5b9cf6;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11pt;
                min-height: 36px;
            }
            QPushButton:hover {
                background-color: #4a90e2;
            }
            QPushButton:pressed {
                background-color: #3d7dcf;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
            QRadioButton {
                background: transparent;
                padding: 5px;
                color: #333333;
                font-size: 11pt;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #5b9cf6;
            }
            QRadioButton::indicator:checked {
                background-color: #5b9cf6;
                width: 10px;
                height: 10px;
                border-radius: 5px;
                border: 6px solid #5b9cf6;
            }
            QListWidget {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 8px;
                background-color: #ffffff;
                font-size: 11pt;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #f0f0f0;
                margin-bottom: 2px;
                border-radius: 5px;
            }
            QListWidget::item:selected {
                background-color: #e8f0fe;
                color: #333333;
            }
            QListWidget::item:hover {
                background-color: #f5f5f7;
            }
            QMenu {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                color: #333333;
                font-size: 10pt;
            }
            QMenu::item:selected {
                background-color: #e8f0fe;
            }
            QTimeEdit::up-button, QTimeEdit::down-button {
                width: 20px;
                background-color: #f0f0f0;
                border-radius: 3px;
            }
        """)
        
        # 创建设置区域和任务列表的水平布局
        content_layout = QHBoxLayout()
        
        # 左侧设置面板
        settings_widget = QWidget()
        settings_widget.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 10px;
            }
        """)
        settings_layout = QVBoxLayout(settings_widget)
        settings_layout.setSpacing(15)
        
        # 添加标题
        title_label = QLabel("创建新提醒")
        title_label.setStyleSheet("""
            font-size: 14pt;
            color: #333333;
            margin-bottom: 10px;
            padding: 5px;
            border-bottom: 2px solid #e0e0e0;
        """)
        settings_layout.addWidget(title_label)
        
        # 创建提醒类型选择按钮
        type_container = QWidget()
        type_layout = QVBoxLayout(type_container)
        type_layout.setContentsMargins(10, 10, 10, 10)
        
        type_label = QLabel("提醒方式")
        type_label.setStyleSheet("font-size: 12pt; margin-bottom: 5px;")
        type_layout.addWidget(type_label)
        
        self.type_group = QButtonGroup()
        interval_radio = QRadioButton("间隔提醒")
        time_radio = QRadioButton("定时提醒")
        self.type_group.addButton(interval_radio, 1)
        self.type_group.addButton(time_radio, 2)
        interval_radio.setChecked(True)
        
        radio_layout = QHBoxLayout()
        radio_layout.addWidget(interval_radio)
        radio_layout.addWidget(time_radio)
        type_layout.addLayout(radio_layout)
        settings_layout.addWidget(type_container)
        
        # 间隔输入
        self.interval_widget = QWidget()
        self.interval_widget.setStyleSheet("""
            QWidget {
                background-color: #f9f9f9;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        interval_layout = QVBoxLayout(self.interval_widget)
        interval_layout.setContentsMargins(10, 10, 10, 10)
        interval_label = QLabel("提醒间隔（分钟）：")
        self.interval_input = QLineEdit()
        self.interval_input.setPlaceholderText("请输入分钟数")
        interval_layout.addWidget(interval_label)
        interval_layout.addWidget(self.interval_input)
        settings_layout.addWidget(self.interval_widget)
        
        # 时间输入
        self.time_widget = QWidget()
        self.time_widget.setStyleSheet("""
            QWidget {
                background-color: #f9f9f9;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        time_layout = QVBoxLayout(self.time_widget)
        time_layout.setContentsMargins(10, 10, 10, 10)
        time_label = QLabel("提醒时间：")
        self.time_input = QTimeEdit()
        self.time_input.setTime(QTime.currentTime().addSecs(60))  # 默认设置为当前时间后一分钟
        self.time_input.setDisplayFormat("HH:mm")
        time_layout.addWidget(time_label)
        time_layout.addWidget(self.time_input)
        settings_layout.addWidget(self.time_widget)
        self.time_widget.hide()
        
        # 消息输入
        message_widget = QWidget()
        message_widget.setStyleSheet("""
            QWidget {
                background-color: #f9f9f9;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        message_layout = QVBoxLayout(message_widget)
        message_layout.setContentsMargins(10, 10, 10, 10)
        message_label = QLabel("提醒内容：")
        self.message_input = QLineEdit()
        self.message_input.setPlaceholderText("请输入提醒内容")
        message_layout.addWidget(message_label)
        message_layout.addWidget(self.message_input)
        settings_layout.addWidget(message_widget)
        
        # 按钮容器
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(10, 10, 10, 10)
        
        # 添加提醒按钮
        self.add_button = QPushButton("添加提醒")
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #5b9cf6;
                min-width: 120px;
            }
        """)
        self.add_button.clicked.connect(self.add_reminder)
        button_layout.addWidget(self.add_button)
        
        settings_layout.addWidget(button_widget)
        
        # 填充空白
        settings_layout.addStretch()
        
        # 右侧提醒列表
        list_container = QWidget()
        list_container.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 10px;
            }
        """)
        list_layout = QVBoxLayout(list_container)
        list_layout.setContentsMargins(15, 15, 15, 15)
        
        list_title = QLabel("提醒列表")
        list_title.setStyleSheet("""
            font-size: 14pt;
            color: #333333;
            margin-bottom: 10px;
            padding: 5px;
            border-bottom: 2px solid #e0e0e0;
        """)
        list_layout.addWidget(list_title)
        
        self.reminder_list = QListWidget()
        self.reminder_list.setSelectionMode(QListWidget.SelectionMode.SingleSelection)
        self.reminder_list.setStyleSheet("""
            QListWidget {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 5px;
                font-size: 11pt;
            }
        """)
        list_layout.addWidget(self.reminder_list)
        
        # 列表操作按钮
        list_buttons = QWidget()
        list_buttons_layout = QHBoxLayout(list_buttons)
        
        self.delete_button = QPushButton("删除提醒")
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #ff5d5d;
            }
            QPushButton:hover {
                background-color: #e54545;
            }
            QPushButton:pressed {
                background-color: #d03e3e;
            }
        """)
        self.delete_button.clicked.connect(self.delete_reminder)
        list_buttons_layout.addWidget(self.delete_button)
        
        list_layout.addWidget(list_buttons)
        
        # 将左侧设置面板和右侧列表添加到水平布局
        content_layout.addWidget(settings_widget, 1)
        content_layout.addWidget(list_container, 1)
        
        # 将水平布局添加到主布局
        main_layout.addLayout(content_layout)
        
        # 添加底部状态信息
        status_label = QLabel("提示：您可以在系统托盘中找到更多选项")
        status_label.setStyleSheet("""
            color: #777777;
            background: transparent;
            font-size: 9pt;
            padding: 5px;
        """)
        status_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(status_label)
        
        # 连接信号
        self.type_group.buttonClicked.connect(self.on_type_changed)
        
        # 设置系统托盘
        self.setup_tray_icon()
        
        # 设置定时器用于更新列表中的剩余时间显示
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_reminder_list)
        self.update_timer.start(1000)  # 每秒更新一次
        
    def create_default_icon(self):
        """创建或加载应用图标"""
        icon_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "resources")
        icon_path = os.path.join(icon_dir, "reminder_icon.png")
        
        # 如果图标文件存在就直接返回路径
        if os.path.exists(icon_path):
            return icon_path
            
        # 如果图标不存在，创建一个基本图标并保存
        try:
            # 确保目录存在
            os.makedirs(icon_dir, exist_ok=True)
            
            # 使用PySide6创建一个简单的图标图像
            from PySide6.QtGui import QPixmap, QPainter, QBrush, QPen, QColor, QRadialGradient, QLinearGradient
            from PySide6.QtCore import QPoint, QRect
            
            # 创建图像
            size = 128
            pixmap = QPixmap(size, size)
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # 创建渐变背景
            gradient = QRadialGradient(size/2, size/2, size/2)
            gradient.setColorAt(0, QColor("#6ebff5"))
            gradient.setColorAt(1, QColor("#5b9cf6"))
            
            # 绘制圆形背景
            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(4, 4, size-8, size-8)
            
            # 绘制边框
            painter.setPen(QPen(QColor("#4a90e2"), 2))
            painter.drawEllipse(4, 4, size-8, size-8)
            
            # 绘制中心钟表外圈
            painter.setBrush(QBrush(QColor("#ffffff")))
            painter.setPen(QPen(QColor("#e0e0e0"), 2))
            clock_size = size * 0.7
            clock_x = (size - clock_size) / 2
            clock_y = (size - clock_size) / 2
            painter.drawEllipse(clock_x, clock_y, clock_size, clock_size)
            
            # 绘制时钟刻度
            painter.setPen(QPen(QColor("#555555"), 2))
            center_x = size / 2
            center_y = size / 2
            radius = clock_size / 2 - 4
            
            for i in range(12):
                angle = i * 30 * 3.14159 / 180
                outer_x = center_x + radius * 0.9 * qCos(angle)
                outer_y = center_y + radius * 0.9 * qSin(angle)
                inner_x = center_x + radius * 0.8 * qCos(angle)
                inner_y = center_y + radius * 0.8 * qSin(angle)
                painter.drawLine(outer_x, outer_y, inner_x, inner_y)
            
            # 绘制时针和分针
            # 时针（短）
            painter.setPen(QPen(QColor("#333333"), 3))
            hour_angle = 240 * 3.14159 / 180  # 4点
            hour_x = center_x + radius * 0.5 * qCos(hour_angle)
            hour_y = center_y + radius * 0.5 * qSin(hour_angle)
            painter.drawLine(center_x, center_y, hour_x, hour_y)
            
            # 分针（长）
            painter.setPen(QPen(QColor("#333333"), 2))
            minute_angle = 30 * 3.14159 / 180  # 1点30分
            minute_x = center_x + radius * 0.7 * qCos(minute_angle)
            minute_y = center_y + radius * 0.7 * qSin(minute_angle)
            painter.drawLine(center_x, center_y, minute_x, minute_y)
            
            # 中心点
            painter.setBrush(QBrush(QColor("#ff5d5d")))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(center_x-3, center_y-3, 6, 6)
            
            painter.end()
            
            # 保存图像
            pixmap.save(icon_path)
            print(f"已创建图标: {icon_path}")
            return icon_path
            
        except Exception as e:
            print(f"创建图标失败: {e}")
            return None
    
    def setup_tray_icon(self):
        # 创建系统托盘图标
        self.tray_icon = QSystemTrayIcon(self)
        self.tray_icon.setIcon(self.app_icon)
        self.tray_icon.setToolTip("提醒助手")
        
        # 创建托盘菜单
        tray_menu = QMenu()
        
        show_action = QAction("显示主窗口", self)
        show_action.triggered.connect(self.show_main_window)
        tray_menu.addAction(show_action)
        
        # 添加开机自启动选项
        self.autostart_action = QAction("开机自启动", self)
        self.autostart_action.setCheckable(True)
        self.autostart_action.setChecked(self.is_autostart_enabled())
        self.autostart_action.triggered.connect(self.toggle_autostart)
        tray_menu.addAction(self.autostart_action)
        
        tray_menu.addSeparator()
        
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.quit_application)
        tray_menu.addAction(exit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.tray_icon_activated)
        
        # 显示托盘图标
        self.tray_icon.show()
    
    def tray_icon_activated(self, reason):
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show_main_window()
    
    def show_main_window(self):
        self.showNormal()
        self.activateWindow()
    
    def quit_application(self):
        # 退出前保存数据
        self.save_reminders()
        QApplication.quit()
    
    def closeEvent(self, event: QCloseEvent):
        # 重写关闭事件，点击窗口关闭按钮时最小化到托盘
        # 保存当前提醒数据
        self.save_reminders()
        
        event.ignore()
        self.hide()
        self.tray_icon.showMessage(
            "提醒助手",
            "应用程序已最小化到系统托盘，双击托盘图标可以重新打开窗口。",
            QSystemTrayIcon.MessageIcon.Information,
            2000
        )
        
    def on_type_changed(self, button):
        if self.type_group.checkedId() == 1:  # 间隔
            self.interval_widget.show()
            self.time_widget.hide()
        else:  # 定时
            self.interval_widget.hide()
            self.time_widget.show()
    
    def add_reminder(self):
        message = self.message_input.text()
        if not message:
            QMessageBox.warning(self, "错误", "请输入提醒内容！")
            return
            
        is_interval = self.type_group.checkedId() == 1
        
        if is_interval:  # 间隔提醒
            try:
                interval_minutes = int(self.interval_input.text())
                if interval_minutes <= 0:
                    raise ValueError
                interval_ms = interval_minutes * 60 * 1000  # 转换为毫秒
            except ValueError:
                QMessageBox.warning(self, "错误", "请输入有效的正整数！")
                return
                
            # 创建提醒项
            reminder = ReminderItem(message, True, interval_ms)
            
            # 创建新的定时器
            timer_id = hash(f"{message}_{datetime.datetime.now().timestamp()}")
            timer = QTimer(self)
            # 使用partial避免lambda引起的闭包问题
            timer.timeout.connect(lambda r=reminder: self.show_reminder(r))
            
            # 存储定时器和提醒
            self.timers[timer_id] = timer
            reminder.timer_id = timer_id
            reminder.last_triggered = datetime.datetime.now()
            reminder.next_trigger = reminder.last_triggered + datetime.timedelta(milliseconds=interval_ms)
            self.reminders.append(reminder)
            
            # 立即显示第一次提醒
            self.show_reminder(reminder)
            
        else:  # 定时提醒
            target_time = self.time_input.time().toPython()
            
            # 转换为datetime以进行比较
            now = datetime.datetime.now()
            target = datetime.datetime.combine(now.date(), target_time)
            
            # 如果时间已过，设置为明天
            if target <= now:
                target += datetime.timedelta(days=1)
            
            # 计算毫秒数
            delta = target - now
            msecs = int(delta.total_seconds() * 1000)
            
            # 创建提醒项
            reminder = ReminderItem(message, False, 0, target)
            reminder.next_trigger = target
            
            # 创建新的定时器
            timer_id = hash(f"{message}_{target.timestamp()}")
            timer = QTimer(self)
            timer.setSingleShot(True)
            # 使用partial避免lambda引起的闭包问题
            timer.timeout.connect(lambda r=reminder: self.show_reminder(r))
            timer.start(msecs)
            
            # 存储定时器和提醒
            self.timers[timer_id] = timer
            reminder.timer_id = timer_id
            self.reminders.append(reminder)
        
        # 更新列表
        self.update_reminder_list()
        
        # 保存提醒数据
        self.save_reminders()
        
        # 清空输入
        self.message_input.clear()
        if is_interval:
            self.interval_input.clear()
            
    def update_reminder_list(self):
        # 保存当前选中的项
        current_selected = -1
        if self.reminder_list.selectedItems():
            selected_item = self.reminder_list.selectedItems()[0]
            current_selected = selected_item.data(Qt.ItemDataRole.UserRole)
        
        self.reminder_list.clear()
        for reminder in self.reminders:
            if not reminder.active:
                continue
                
            item = QListWidgetItem(reminder.get_display_text())
            item.setData(Qt.ItemDataRole.UserRole, self.reminders.index(reminder))
            self.reminder_list.addItem(item)
            
            # 如果这个项之前是选中的，再次选中它
            if self.reminders.index(reminder) == current_selected:
                self.reminder_list.setCurrentItem(item)
    
    def delete_reminder(self):
        selected_items = self.reminder_list.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "提示", "请先选择要删除的提醒")
            return
            
        selected_item = selected_items[0]
        reminder_index = selected_item.data(Qt.ItemDataRole.UserRole)
        
        if 0 <= reminder_index < len(self.reminders):
            reminder = self.reminders[reminder_index]
            
            # 停止并删除定时器
            if reminder.timer_id in self.timers:
                timer = self.timers[reminder.timer_id]
                timer.stop()
                del self.timers[reminder.timer_id]
            
            # 标记为非活动
            reminder.active = False
            
            # 关闭该提醒的对话框(如果存在)
            if reminder.timer_id in self.active_reminder_dialogs:
                dialog = self.active_reminder_dialogs[reminder.timer_id]
                dialog.close()
                del self.active_reminder_dialogs[reminder.timer_id]
            
            # 更新列表
            self.update_reminder_list()
            
            # 保存提醒数据
            self.save_reminders()
        
    def show_reminder(self, reminder):
        """显示提醒窗口，必须用户手动关闭，且不会影响主程序"""
        # 如果不是活动的提醒，就不显示
        if not reminder.active:
            return
        
        # 如果已经有该提醒的对话框正在显示，则不再创建新的
        if reminder.timer_id in self.active_reminder_dialogs:
            existing_dialog = self.active_reminder_dialogs[reminder.timer_id]
            if existing_dialog.isVisible():
                existing_dialog.raise_()
                existing_dialog.activateWindow()
                return
            
        # 更新最后触发时间
        reminder.last_triggered = datetime.datetime.now()
        
        # 创建自定义对话框
        dialog = ReminderDialog(reminder)
        
        # 连接信号
        if reminder.is_interval:
            dialog.confirmClicked.connect(self.restart_interval_timer)
        else:
            dialog.confirmClicked.connect(self.remove_one_time_reminder)
        
        # 存储对话框引用，防止被垃圾回收
        self.active_reminder_dialogs[reminder.timer_id] = dialog
        
        # 显示对话框并确保它在前台
        dialog.show()
        dialog.raise_()
        dialog.activateWindow()
        
    def restart_interval_timer(self, reminder):
        """
        当用户关闭间隔提醒的弹窗后，重新开始计时
        从用户手动关闭提醒的那一刻开始计时
        """
        # 如果提醒已被删除，不重启
        if not reminder.active:
            return
            
        # 重启定时器，从用户关闭提醒窗口的那一刻开始计时
        if reminder.timer_id in self.timers:
            timer = self.timers[reminder.timer_id]
            timer.start(reminder.interval)
            
            # 更新下一次触发时间用于显示
            now = datetime.datetime.now()
            reminder.next_trigger = now + datetime.timedelta(milliseconds=reminder.interval)
            
            # 清除对话框引用 - 安全处理
            if reminder.timer_id in self.active_reminder_dialogs:
                dialog = self.active_reminder_dialogs[reminder.timer_id]
                dialog.deleteLater()  # 安全地删除对话框
                del self.active_reminder_dialogs[reminder.timer_id]
            
            # 更新列表显示并保存数据
            self.update_reminder_list()
            self.save_reminders()
            
    def remove_one_time_reminder(self, reminder):
        # 标记提醒为非活动
        reminder.active = False
        
        # 清除对话框引用 - 安全处理
        if reminder.timer_id in self.active_reminder_dialogs:
            dialog = self.active_reminder_dialogs[reminder.timer_id]
            dialog.deleteLater()  # 安全地删除对话框
            del self.active_reminder_dialogs[reminder.timer_id]
        
        # 更新列表显示并保存数据
        self.update_reminder_list()
        self.save_reminders()

    def save_reminders(self):
        """保存提醒数据到文件"""
        try:
            # 将提醒对象转换为可序列化的字典
            reminders_data = [reminder.to_dict() for reminder in self.reminders if reminder.active]
            
            # 保存到文件
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(reminders_data, f, ensure_ascii=False, indent=2)
                
            print(f"已保存 {len(reminders_data)} 个提醒到 {self.data_file}")
        except Exception as e:
            print(f"保存提醒数据失败: {e}")
    
    def load_reminders(self):
        """从文件加载提醒数据"""
        if not os.path.exists(self.data_file):
            print(f"提醒数据文件不存在: {self.data_file}")
            return
            
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                reminders_data = json.load(f)
                
            # 清除当前的提醒
            self.reminders = []
            
            # 加载提醒数据
            for data in reminders_data:
                reminder = ReminderItem.from_dict(data)
                
                # 创建新的定时器
                if reminder.active:
                    self._create_timer_for_reminder(reminder)
                
                self.reminders.append(reminder)
                
            # 更新列表
            self.update_reminder_list()
            print(f"已加载 {len(self.reminders)} 个提醒")
        except Exception as e:
            print(f"加载提醒数据失败: {e}")
    
    def _create_timer_for_reminder(self, reminder):
        """为提醒创建定时器"""
        if reminder.is_interval:
            # 计算下一次触发的时间
            if reminder.next_trigger and reminder.next_trigger > datetime.datetime.now():
                # 如果有保存的下一次触发时间，并且还未到达
                delta = (reminder.next_trigger - datetime.datetime.now()).total_seconds() * 1000
                msecs = int(max(0, delta))  # 确保不是负数
            else:
                # 否则立即触发
                msecs = 0
                
            # 创建新的定时器
            timer_id = hash(f"{reminder.message}_{datetime.datetime.now().timestamp()}")
            timer = QTimer(self)
            # 使用partial避免lambda引起的闭包问题
            timer.timeout.connect(lambda r=reminder: self.show_reminder(r))
            timer.start(msecs)
            
            # 存储定时器和提醒
            self.timers[timer_id] = timer
            reminder.timer_id = timer_id
            
            if not reminder.next_trigger or reminder.next_trigger < datetime.datetime.now():
                reminder.next_trigger = datetime.datetime.now() + datetime.timedelta(milliseconds=reminder.interval)
        else:
            # 定时提醒
            if reminder.target_time:
                # 转换为datetime以进行比较
                now = datetime.datetime.now()
                target_date = now.date()
                target_time = reminder.target_time.time()
                target = datetime.datetime.combine(target_date, target_time)
                
                # 如果时间已过，设置为明天
                if target <= now:
                    target += datetime.timedelta(days=1)
                
                # 计算毫秒数
                delta = target - now
                msecs = int(delta.total_seconds() * 1000)
                
                # 更新提醒的目标时间
                reminder.target_time = target
                reminder.next_trigger = target
                
                # 创建新的定时器
                timer_id = hash(f"{reminder.message}_{target.timestamp()}")
                timer = QTimer(self)
                timer.setSingleShot(True)
                # 使用partial避免lambda引起的闭包问题
                timer.timeout.connect(lambda r=reminder: self.show_reminder(r))
                timer.start(msecs)
                
                # 存储定时器和提醒
                self.timers[timer_id] = timer
                reminder.timer_id = timer_id

    def is_autostart_enabled(self):
        """检查应用是否已设置为开机自启动"""
        try:
            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r'Software\Microsoft\Windows\CurrentVersion\Run',
                0,
                winreg.KEY_READ
            ) as key:
                winreg.QueryValueEx(key, self.app_name)
                return True
        except FileNotFoundError:
            return False
        except WindowsError:
            return False
            
    def toggle_autostart(self):
        """切换开机自启动状态"""
        if self.autostart_action.isChecked():
            self.enable_autostart()
        else:
            self.disable_autostart()
            
    def enable_autostart(self):
        """设置开机自启动"""
        try:
            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r'Software\Microsoft\Windows\CurrentVersion\Run',
                0,
                winreg.KEY_WRITE
            ) as key:
                winreg.SetValueEx(key, self.app_name, 0, winreg.REG_SZ, self.app_path)
            self.tray_icon.showMessage(
                "提醒助手", 
                "已设置开机自启动",
                QSystemTrayIcon.MessageIcon.Information,
                2000
            )
        except Exception as e:
            QMessageBox.warning(self, "错误", f"设置开机自启动失败: {str(e)}")
            self.autostart_action.setChecked(False)
            
    def disable_autostart(self):
        """禁用开机自启动"""
        try:
            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r'Software\Microsoft\Windows\CurrentVersion\Run',
                0,
                winreg.KEY_WRITE
            ) as key:
                try:
                    winreg.DeleteValue(key, self.app_name)
                except FileNotFoundError:
                    pass  # 键不存在，忽略错误
            self.tray_icon.showMessage(
                "提醒助手", 
                "已禁用开机自启动",
                QSystemTrayIcon.MessageIcon.Information,
                2000
            )
        except Exception as e:
            QMessageBox.warning(self, "错误", f"禁用开机自启动失败: {str(e)}")
            self.autostart_action.setChecked(True)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = ReminderApp()
    
    # 加载保存的提醒数据
    window.load_reminders()
    
    window.show()
    sys.exit(app.exec()) 