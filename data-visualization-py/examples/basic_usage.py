import sys
import os

# Add the project root directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.data_loader import DataLoader
from src.visualizers.line_chart import Line<PERSON><PERSON>

def main():
    """
    Demonstrate basic usage of the visualization tools.
    """
    # Create data and output directories if they don't exist
    data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
    output_dir = os.path.join(os.path.dirname(__file__), "output")
    os.makedirs(data_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    # Load sample data
    data = DataLoader.from_csv(os.path.join(data_dir, "sample_data.csv"))
    
    # Create a line chart
    chart = LineChart(data)
    
    # Create a simple line chart
    chart.plot(
        x="date",
        y="value",
        title="Sample Time Series Data"
    )
    
    # Save the chart
    chart.save(os.path.join(output_dir, "simple_line_chart.html"), format="html")
    
    # Create a line chart with categories
    chart.plot(
        x="date",
        y="value",
        color="category",
        title="Time Series Data by Category"
    )
    
    # Save the chart
    chart.save(os.path.join(output_dir, "categorical_line_chart.html"), format="html")
    
    print("Charts have been generated in the examples/output directory!")

if __name__ == "__main__":
    main() 