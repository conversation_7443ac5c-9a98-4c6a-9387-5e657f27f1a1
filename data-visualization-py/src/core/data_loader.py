import pandas as pd
from typing import Union, Optional
import json

class DataLoader:
    """
    A class for loading and managing data for visualization.
    """
    
    def __init__(self, data: pd.DataFrame):
        """
        Initialize the DataLoader with a pandas DataFrame.
        
        Args:
            data (pd.DataFrame): The data to be visualized
        """
        self.data = data
    
    @classmethod
    def from_csv(cls, filepath: str, **kwargs) -> 'DataLoader':
        """
        Create a DataLoader instance from a CSV file.
        
        Args:
            filepath (str): Path to the CSV file
            **kwargs: Additional arguments to pass to pd.read_csv
            
        Returns:
            DataLoader: A new DataLoader instance containing the CSV data
        """
        data = pd.read_csv(filepath, **kwargs)
        return cls(data)
    
    def get_column(self, column: str) -> pd.Series:
        """
        Get a specific column from the data.
        
        Args:
            column (str): Name of the column to retrieve
            
        Returns:
            pd.Series: The requested column data
        """
        return self.data[column]
    
    def get_unique_values(self, column: str) -> list:
        """
        Get unique values from a specific column.
        
        Args:
            column (str): Name of the column
            
        Returns:
            list: List of unique values in the column
        """
        return self.data[column].unique().tolist()
    
    @staticmethod
    def from_excel(filepath: str, sheet_name: Optional[Union[str, int]] = 0, **kwargs) -> pd.DataFrame:
        """
        Load data from an Excel file.
        
        Args:
            filepath (str): Path to the Excel file
            sheet_name: Name or index of the sheet to load
            **kwargs: Additional arguments to pass to pd.read_excel
            
        Returns:
            pd.DataFrame: Loaded data
        """
        return pd.read_excel(filepath, sheet_name=sheet_name, **kwargs)
    
    @staticmethod
    def from_json(filepath: str, **kwargs) -> pd.DataFrame:
        """
        Load data from a JSON file.
        
        Args:
            filepath (str): Path to the JSON file
            **kwargs: Additional arguments to pass to pd.read_json
            
        Returns:
            pd.DataFrame: Loaded data
        """
        return pd.read_json(filepath, **kwargs)
    
    @staticmethod
    def to_csv(data: pd.DataFrame, filepath: str, **kwargs) -> None:
        """
        Save data to a CSV file.
        
        Args:
            data (pd.DataFrame): Data to save
            filepath (str): Path to save the CSV file
            **kwargs: Additional arguments to pass to df.to_csv
        """
        data.to_csv(filepath, index=False, **kwargs) 