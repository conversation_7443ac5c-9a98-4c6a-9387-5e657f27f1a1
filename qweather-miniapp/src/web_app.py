"""Weather forecast web application."""
from flask import Flask, render_template, request, jsonify
from api.weather_service import WeatherService
from models.weather import WeatherForecast

app = Flask(__name__)
weather_service = WeatherService()


@app.route('/')
def index():
    """Render the main page."""
    return render_template('index.html')


@app.route('/weather')
def get_weather():
    """Get weather data."""
    try:
        city = request.args.get("city", "").strip()
        print(f"接收到天气查询请求，城市: {city}")
        
        weather_service = WeatherService()
        
        # 如果没有指定城市，使用定位的城市
        if not city:
            city = weather_service.get_current_location()
            print(f"使用当前定位城市: {city}")
        
        # 获取当前天气和空气质量
        try:
            current_weather, air_data, hourly_data = weather_service.get_current_weather(city)
        except Exception as e:
            print(f"获取当前天气失败: {str(e)}")
            return jsonify({
                "success": False,
                "error": f"获取天气信息失败: {str(e)}"
            }), 500
        
        # 获取7天预报
        try:
            forecast = weather_service.get_forecast(city)
        except Exception as e:
            print(f"获取天气预报失败: {str(e)}")
            return jsonify({
                "success": False,
                "error": f"获取天气预报失败: {str(e)}"
            }), 500
        
        # 构建响应数据
        response_data = {
            "success": True,
            "city": city,
            "current": {
                "temperature": current_weather["now"]["temp"],
                "description": current_weather["now"]["text"],
                "wind_speed": current_weather["now"]["windSpeed"],
                "wind_direction": current_weather["now"]["windDir"],
                "humidity": current_weather["now"]["humidity"],
                "air_quality": air_data["now"]["category"],
                "visibility": current_weather["now"].get("vis"),
                "pressure": current_weather["now"].get("pressure"),
                "cloud": current_weather["now"].get("cloud"),
                "temperature_feels_like": current_weather["now"].get("feelsLike")
            },
            "hourly": [
                {
                    "time": hour["fxTime"],
                    "temperature": hour["temp"],
                    "description": hour["text"]
                }
                for hour in hourly_data.get("hourly", [])
            ],
            "daily": [
                {
                    "date": day["fxDate"],
                    "temperature_max": day["tempMax"],
                    "temperature_min": day["tempMin"],
                    "description": day["textDay"],
                    "wind_speed": day.get("windSpeedDay"),
                    "humidity": day.get("humidity"),
                    "visibility": day.get("vis"),
                    "pressure": day.get("pressure"),
                    "cloud": day.get("cloud"),
                    "temperature_feels_like": day.get("feelsLike")
                }
                for day in forecast.get("daily", [])
            ]
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        print(f"处理天气请求时发生错误: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"获取天气信息失败: {str(e)}"
        }), 500


if __name__ == '__main__':
    app.run(debug=True) 