"""缓存管理模块，用于缓存天气数据"""
import os
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional, Tuple


class Cache:
    """缓存管理类，提供天气数据的缓存功能"""

    def __init__(self, cache_dir: str, expiration_time: int = 1800) -> None:
        """
        初始化缓存管理器
        
        Args:
            cache_dir: 缓存文件存储目录
            expiration_time: 缓存过期时间(秒)，默认30分钟
        """
        self.cache_dir = Path(cache_dir)
        self.expiration_time = expiration_time
        self.stats = {
            "hits": 0,  # 缓存命中次数
            "misses": 0,  # 缓存未命中次数
            "writes": 0,  # 缓存写入次数
        }
        
        # 确保缓存目录存在
        self._ensure_cache_dir()
    
    def _ensure_cache_dir(self) -> None:
        """确保缓存目录存在，如不存在则创建"""
        if not self.cache_dir.exists():
            os.makedirs(self.cache_dir, exist_ok=True)
            print(f"✓ 已创建缓存目录: {self.cache_dir}")
    
    def _get_cache_file_path(self, key: str) -> Path:
        """
        获取缓存文件路径
        
        Args:
            key: 缓存键名
            
        Returns:
            缓存文件路径
        """
        # 使用MD5或其他哈希可以处理特殊字符，但这里为简单起见直接使用键名
        safe_key = key.replace(" ", "_").replace("/", "_").replace("\\", "_")
        return self.cache_dir / f"{safe_key}.json"
    
    def get(self, key: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        获取缓存数据
        
        Args:
            key: 缓存键名
            
        Returns:
            Tuple(是否命中缓存, 缓存的数据(如果命中))
        """
        cache_file = self._get_cache_file_path(key)
        
        # 检查缓存文件是否存在
        if not cache_file.exists():
            self.stats["misses"] += 1
            return False, None
        
        try:
            # 读取缓存文件
            with open(cache_file, "r", encoding="utf-8") as f:
                cache_data = json.load(f)
            
            # 检查缓存是否过期
            if time.time() - cache_data["timestamp"] > self.expiration_time:
                print(f"⚠ 缓存已过期: {key}")
                self.stats["misses"] += 1
                return False, None
            
            # 缓存命中
            print(f"✓ 缓存命中: {key}")
            self.stats["hits"] += 1
            return True, cache_data["data"]
            
        except Exception as e:
            print(f"✗ 读取缓存出错: {str(e)}")
            self.stats["misses"] += 1
            return False, None
    
    def set(self, key: str, data: Dict[str, Any]) -> bool:
        """
        设置缓存数据
        
        Args:
            key: 缓存键名
            data: 要缓存的数据
            
        Returns:
            是否成功写入缓存
        """
        cache_file = self._get_cache_file_path(key)
        
        try:
            # 写入缓存数据
            cache_data = {
                "timestamp": time.time(),
                "data": data
            }
            
            with open(cache_file, "w", encoding="utf-8") as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            print(f"✓ 缓存已写入: {key}")
            self.stats["writes"] += 1
            return True
            
        except Exception as e:
            print(f"✗ 写入缓存出错: {str(e)}")
            return False
    
    def invalidate(self, key: str) -> bool:
        """
        使特定缓存失效
        
        Args:
            key: 缓存键名
            
        Returns:
            是否成功使缓存失效
        """
        cache_file = self._get_cache_file_path(key)
        
        if cache_file.exists():
            try:
                os.remove(cache_file)
                print(f"✓ 缓存已失效: {key}")
                return True
            except Exception as e:
                print(f"✗ 使缓存失效出错: {str(e)}")
                return False
        return True  # 如果缓存不存在，视为成功
    
    def clear_all(self) -> bool:
        """
        清除所有缓存
        
        Returns:
            是否成功清除所有缓存
        """
        try:
            # 删除缓存目录下的所有json文件
            for cache_file in self.cache_dir.glob("*.json"):
                os.remove(cache_file)
            
            print(f"✓ 所有缓存已清除")
            return True
            
        except Exception as e:
            print(f"✗ 清除所有缓存出错: {str(e)}")
            return False
    
    def get_stats(self) -> Dict[str, int]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息
        """
        # 计算缓存命中率
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = self.stats["hits"] / total_requests if total_requests > 0 else 0
        
        return {
            **self.stats,
            "total_requests": total_requests,
            "hit_rate": round(hit_rate * 100, 2)  # 转换为百分比并保留两位小数
        }
    
    def create_cache_key(self, prefix: str, **kwargs) -> str:
        """
        创建缓存键，用于区分不同的请求
        
        Args:
            prefix: 键前缀，通常是API端点名称
            **kwargs: 可变参数，用于区分不同的请求
            
        Returns:
            缓存键名
        """
        # 将参数按字母顺序排序，以确保相同参数产生相同的键
        param_str = "&".join(f"{k}={v}" for k, v in sorted(kwargs.items()) if v is not None)
        return f"{prefix}:{param_str}" 