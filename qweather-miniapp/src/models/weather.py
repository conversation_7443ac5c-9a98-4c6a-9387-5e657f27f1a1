"""Weather data models."""
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional


@dataclass
class WeatherCondition:
    """Weather condition data model."""
    
    temperature: float
    temperature_feels_like: float  # 体感温度
    temperature_min: float  # 最低温度（仅预报）
    temperature_max: float  # 最高温度（仅预报）
    humidity: int  # 相对湿度
    pressure: int  # 大气压强
    precipitation: float  # 降水量
    visibility: float  # 能见度
    cloud: int  # 云量
    wind_speed: float
    wind_direction: str
    wind_scale: str  # 风力等级
    description: str
    uv_index: str  # 紫外线指数
    air_quality: str  # 空气质量
    timestamp: datetime

    @classmethod
    def from_api_data(cls, data: dict, time: Optional[str] = None) -> "WeatherCondition":
        """
        Create WeatherCondition instance from API response data.

        Args:
            data: Raw weather data from API
            time: Optional time string for forecast data

        Returns:
            WeatherCondition instance
        """
        # 处理实时天气数据
        if not time:  # 实时天气
            try:
                timestamp = datetime.strptime(
                    data.get("obsTime", datetime.now().strftime("%Y-%m-%dT%H:%M+08:00")),
                    "%Y-%m-%dT%H:%M%z"
                )
            except ValueError:
                # 如果时间格式解析失败，尝试其他格式
                try:
                    timestamp = datetime.strptime(
                        data.get("fxTime", datetime.now().strftime("%Y-%m-%d %H:%M")),
                        "%Y-%m-%d %H:%M"
                    )
                except ValueError:
                    timestamp = datetime.now()

            return cls(
                temperature=float(data.get("temp", 0)),
                temperature_feels_like=float(data.get("feelsLike", 0)),
                temperature_min=float(data.get("temp", 0)),  # 实时天气没有最低温度
                temperature_max=float(data.get("temp", 0)),  # 实时天气没有最高温度
                humidity=int(data.get("humidity", 0)),
                pressure=int(data.get("pressure", 0)),
                precipitation=float(data.get("precip", 0)),
                visibility=float(data.get("vis", 0)),
                cloud=int(data.get("cloud", 0)),
                wind_speed=float(data.get("windSpeed", 0)),
                wind_direction=data.get("windDir", "未知"),
                wind_scale=data.get("windScale", "未知"),
                description=data.get("text", "未知"),
                uv_index=data.get("uvIndex", "未知"),
                air_quality=data.get("category", "未知"),  # 需要额外调用空气质量API
                timestamp=timestamp
            )
        
        # 处理预报数据
        try:
            if "fxTime" in data:  # 小时预报
                timestamp = datetime.strptime(data["fxTime"], "%Y-%m-%dT%H:%M%z")
            else:  # 日预报
                timestamp = datetime.strptime(time, "%Y-%m-%d %H:%M")
        except ValueError:
            timestamp = datetime.now()

        return cls(
            temperature=float(data.get("temp", data.get("tempMax", 0))),  # 优先使用当前温度，否则使用最高温度
            temperature_feels_like=float(data.get("feelsLike", data.get("tempMax", 0))),
            temperature_min=float(data.get("tempMin", 0)),
            temperature_max=float(data.get("tempMax", 0)),
            humidity=int(data.get("humidity", 0)),
            pressure=int(data.get("pressure", 0)),
            precipitation=float(data.get("precip", 0)),
            visibility=float(data.get("vis", 0)),
            cloud=int(data.get("cloud", 0)),
            wind_speed=float(data.get("windSpeed", data.get("windSpeedDay", 0))),
            wind_direction=data.get("windDir", data.get("windDirDay", "未知")),
            wind_scale=data.get("windScale", data.get("windScaleDay", "未知")),
            description=data.get("text", data.get("textDay", "未知")),
            uv_index=data.get("uvIndex", "未知"),
            air_quality="未知",  # 预报数据没有空气质量
            timestamp=timestamp
        )


@dataclass
class WeatherForecast:
    """Weather forecast data model."""
    
    city: str
    current_weather: WeatherCondition
    forecast: List[WeatherCondition]
    hourly: List[WeatherCondition]  # 新增逐小时预报

    @classmethod
    def from_api_data(
        cls,
        city: str,
        current_data: dict,
        forecast_data: dict,
        hourly_data: dict
    ) -> "WeatherForecast":
        """
        Create WeatherForecast instance from API response data.

        Args:
            city: City name
            current_data: Current weather data from API
            forecast_data: Forecast data from API
            hourly_data: Hourly forecast data from API

        Returns:
            WeatherForecast instance
        """
        return cls(
            city=city,
            current_weather=WeatherCondition.from_api_data(current_data["now"]),
            forecast=[
                WeatherCondition.from_api_data(
                    day,
                    f"{day['fxDate']} 12:00"
                )
                for day in forecast_data["daily"]
            ],
            hourly=[
                WeatherCondition.from_api_data(
                    hour,
                    hour["fxTime"]
                )
                for hour in hourly_data.get("hourly", [])
            ]
        ) 