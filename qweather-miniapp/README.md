# QWeather Mini Program

基于和风天气API的天气小程序项目，提供实时天气查询、天气预报等功能。

## 项目结构

```
qweather-miniapp/
├── src/                    # 后端服务源代码
│   ├── api/               # API接口层
│   ├── data/              # 数据文件
│   ├── models/            # 数据模型
│   └── templates/         # 模板文件
├── config/                # 配置文件目录
└── miniprogram/          # 小程序前端代码
    ├── pages/            # 页面文件
    ├── services/         # 服务层
    └── typings/          # TypeScript类型定义
```

## 功能特性

- 实时天气查询
- 天气预报
- 城市管理
- 天气提醒

## 技术栈

- 后端：Python
- 前端：微信小程序 (TypeScript)
- API：和风天气 API

## 开发环境配置

1. 安装Python依赖：
```bash
pip install -r requirements.txt
```

2. 配置和风天气API密钥：
- 在 `config/config.yaml` 中配置你的API密钥

3. 小程序开发：
- 使用微信开发者工具打开 `miniprogram` 目录
- 在项目配置中设置你的小程序 AppID

## 使用说明

1. 启动后端服务：
```bash
python src/main.py
```

2. 使用微信开发者工具打开小程序项目进行开发和调试

## 注意事项

- 请确保正确配置和风天气API密钥
- 遵循微信小程序的发布规范
- 注意API调用频率限制 