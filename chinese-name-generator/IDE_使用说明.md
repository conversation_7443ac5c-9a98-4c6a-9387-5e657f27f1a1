# IDE 使用说明

## 🚀 快速开始（推荐）

### 方法1：使用专用测试脚本
直接运行以下脚本，无需任何配置：

```bash
python simple_ide_test.py
```

这个脚本会自动处理所有路径问题，并测试所有功能。

### 方法2：使用IDE优化脚本
```bash
python run_in_ide.py
```

## 🔧 一次性环境设置

如果您想永久解决IDE导入问题，运行：

```bash
python setup_ide.py
```

然后重启您的IDE。

## 💡 在您自己的代码中使用

### 简单方式（推荐）
```python
import os
import sys

# 添加这几行到您的脚本开头
script_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(script_dir, 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# 然后正常导入
from name_generator import ChineseNameGenerator
from utils import export_to_csv, analyze_names

# 使用生成器
generator = ChineseNameGenerator()
names = generator.generate_batch(1000, 0.5)
export_to_csv(names, "my_names.csv")
```

### 高级方式
```python
import os
import sys

def setup_path():
    """设置模块路径"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    src_dir = os.path.join(current_dir, 'src')
    if src_dir not in sys.path:
        sys.path.insert(0, src_dir)

setup_path()

from name_generator import ChineseNameGenerator
from utils import export_to_csv, analyze_names

def main():
    generator = ChineseNameGenerator()
    
    # 生成单个姓名
    male_name = generator.generate_name('male')
    female_name = generator.generate_name('female')
    print(f"男性姓名: {male_name}")
    print(f"女性姓名: {female_name}")
    
    # 批量生成
    names = generator.generate_batch(1000, gender_ratio=0.52)
    
    # 导出
    export_to_csv(names, "generated_names.csv")
    
    # 分析
    analysis = analyze_names(names)
    print(f"生成了 {analysis['total_count']} 个姓名")
    print(f"男性: {analysis['male_count']} ({analysis['male_ratio']:.1%})")
    print(f"女性: {analysis['female_count']} ({analysis['female_ratio']:.1%})")

if __name__ == "__main__":
    main()
```

## 🐛 常见问题解决

### 问题1：ModuleNotFoundError
**错误**: `ModuleNotFoundError: No module named 'name_generator'`

**解决方案**:
1. 确保您在项目根目录（chinese-name-generator）运行脚本
2. 运行 `python setup_ide.py` 进行一次性设置
3. 或者使用 `python simple_ide_test.py` 测试

### 问题2：AttributeError
**错误**: `AttributeError: module 'utils' has no attribute 'export_to_csv'`

**解决方案**:
1. 检查导入语句是否正确
2. 确保使用了正确的路径设置代码
3. 运行 `python simple_ide_test.py` 验证环境

### 问题3：工作目录问题
**错误**: 文件路径相关错误

**解决方案**:
1. 使用绝对路径设置：
```python
script_dir = os.path.dirname(os.path.abspath(__file__))
```
2. 或者在IDE中设置工作目录为项目根目录

## 📁 项目结构说明

```
chinese-name-generator/
├── src/                          # 核心代码
│   ├── name_generator.py         # 主生成器
│   ├── data_loader.py           # 数据加载
│   ├── utils.py                 # 工具函数
│   └── data/                    # 数据文件
├── examples/                     # 使用示例
├── tests/                       # 单元测试
├── simple_ide_test.py           # 🔥 IDE专用测试脚本
├── run_in_ide.py               # 🔥 IDE优化脚本
├── setup_ide.py                # 🔥 环境设置脚本
└── README.md                    # 项目说明
```

## ✅ 验证安装

运行以下命令验证一切正常：

```bash
python simple_ide_test.py
```

如果看到 "🎉 所有测试完成！"，说明环境配置成功！

## 🎯 核心功能

- **生成单个姓名**: `generator.generate_name('male')` 或 `generator.generate_name('female')`
- **批量生成**: `generator.generate_batch(count, gender_ratio)`
- **导出CSV**: `export_to_csv(names, filename)`
- **数据分析**: `analyze_names(names)`

## 📞 技术支持

如果仍有问题，请：
1. 首先运行 `python simple_ide_test.py` 查看详细错误信息
2. 检查您的Python环境和IDE设置
3. 确保在正确的目录运行脚本
