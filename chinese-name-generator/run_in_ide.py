"""
IDE专用启动脚本

解决IDE中模块导入问题的专用脚本
"""

import sys
import os

# 确保正确的模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')

# 将src目录添加到Python路径的最前面
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# 验证模块导入
try:
    from name_generator import ChineseNameGenerator
    import utils
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本目录: {current_dir}")
    print(f"src目录: {src_dir}")
    print(f"Python路径: {sys.path[:3]}...")
    sys.exit(1)

def demo_basic_functionality():
    """演示基础功能"""
    print("\n🎯 基础功能演示")
    print("-" * 40)
    
    # 创建生成器
    generator = ChineseNameGenerator()
    
    # 生成单个姓名
    print("单个姓名生成:")
    for i in range(3):
        male = generator.generate_name('male')
        female = generator.generate_name('female')
        print(f"  {i+1}. 男: {male} | 女: {female}")
    
    # 批量生成
    print("\n批量生成测试:")
    names = generator.generate_batch(100, 0.5)
    print(f"  生成了 {len(names)} 个姓名")
    
    # 导出测试
    print("\n导出功能测试:")
    utils.export_to_csv(names, "ide_test.csv")
    print("  ✅ CSV导出成功")
    
    # 分析测试
    analysis = utils.analyze_names(names)
    print(f"\n分析结果:")
    print(f"  男性: {analysis['male_count']} | 女性: {analysis['female_count']}")
    print(f"  不同姓氏: {analysis['unique_surnames']}")

def demo_batch_generation():
    """演示批量生成"""
    print("\n⚡ 批量生成演示")
    print("-" * 40)
    
    generator = ChineseNameGenerator()
    
    # 测试不同规模
    test_sizes = [1000, 5000]
    
    for size in test_sizes:
        print(f"\n生成 {size:,} 个姓名...")
        
        import time
        start_time = time.time()
        names = generator.generate_batch(size, 0.52)
        duration = time.time() - start_time
        speed = size / duration
        
        print(f"  耗时: {duration:.2f}秒")
        print(f"  速度: {speed:,.0f} 个/秒")
        
        # 导出
        filename = f"ide_batch_{size}.csv"
        utils.export_to_csv(names, filename)
        print(f"  ✅ 已导出到 {filename}")

def main():
    """主函数"""
    print("🎉 中文姓名生成器 - IDE专用演示")
    print("=" * 50)
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本位置: {current_dir}")
    
    try:
        demo_basic_functionality()
        demo_batch_generation()
        
        print("\n🎊 所有功能测试完成！")
        print("\n生成的文件:")
        for file in ["ide_test.csv", "ide_batch_1000.csv", "ide_batch_5000.csv"]:
            if os.path.exists(file):
                print(f"  📄 {file}")
        
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
