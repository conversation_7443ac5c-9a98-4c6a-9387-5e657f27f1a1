"""
IDE设置脚本

一次性解决IDE中的模块导入问题
"""

import os
import sys

def setup_python_path():
    """设置Python路径"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    src_dir = os.path.join(current_dir, 'src')
    
    # 创建.pth文件来永久添加路径
    pth_content = f"{src_dir}\n"
    
    # 在项目根目录创建.pth文件
    pth_file = os.path.join(current_dir, 'chinese_name_generator.pth')
    with open(pth_file, 'w') as f:
        f.write(pth_content)
    
    print(f"✅ 已创建路径文件: {pth_file}")
    return src_dir

def create_ide_config():
    """创建IDE配置文件"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 创建.vscode配置（如果使用VSCode）
    vscode_dir = os.path.join(current_dir, '.vscode')
    if not os.path.exists(vscode_dir):
        os.makedirs(vscode_dir)
    
    # settings.json
    settings = {
        "python.defaultInterpreterPath": sys.executable,
        "python.terminal.activateEnvironment": True,
        "python.analysis.extraPaths": ["./src"],
        "python.autoComplete.extraPaths": ["./src"]
    }
    
    import json
    settings_file = os.path.join(vscode_dir, 'settings.json')
    with open(settings_file, 'w') as f:
        json.dump(settings, f, indent=2)
    
    print(f"✅ 已创建VSCode配置: {settings_file}")

def create_init_files():
    """创建__init__.py文件"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 确保所有目录都有__init__.py
    dirs_to_init = [
        current_dir,
        os.path.join(current_dir, 'src'),
        os.path.join(current_dir, 'examples'),
        os.path.join(current_dir, 'tests')
    ]
    
    for dir_path in dirs_to_init:
        if os.path.exists(dir_path):
            init_file = os.path.join(dir_path, '__init__.py')
            if not os.path.exists(init_file):
                with open(init_file, 'w') as f:
                    f.write('# -*- coding: utf-8 -*-\n')
                print(f"✅ 已创建: {init_file}")

def test_imports():
    """测试模块导入"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    src_dir = os.path.join(current_dir, 'src')
    
    if src_dir not in sys.path:
        sys.path.insert(0, src_dir)
    
    try:
        from name_generator import ChineseNameGenerator
        import utils
        print("✅ 模块导入测试成功")
        
        # 快速功能测试
        generator = ChineseNameGenerator()
        name = generator.generate_name('male')
        print(f"✅ 功能测试成功: 生成姓名 '{name}'")
        
        return True
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 中文姓名生成器 - IDE环境设置")
    print("=" * 40)
    
    # 设置Python路径
    src_dir = setup_python_path()
    
    # 创建IDE配置
    create_ide_config()
    
    # 创建__init__.py文件
    create_init_files()
    
    # 测试导入
    if test_imports():
        print("\n🎉 IDE环境设置完成！")
        print("\n使用说明:")
        print("1. 重启您的IDE")
        print("2. 直接运行 run_in_ide.py 测试功能")
        print("3. 或者在您的代码中直接导入:")
        print("   from name_generator import ChineseNameGenerator")
        print("   from utils import export_to_csv")
    else:
        print("\n❌ 设置过程中出现问题，请检查错误信息")

if __name__ == "__main__":
    main()
