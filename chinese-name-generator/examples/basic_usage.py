"""
基础使用示例

演示中文姓名生成器的基本功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# 修改导入方式以避免相对导入问题
import name_generator
import utils


def main():
    """主函数"""
    print("中文姓名生成器 - 基础使用示例")
    print("=" * 40)
    
    # 创建生成器实例
    generator = name_generator.ChineseNameGenerator()
    
    # 获取统计信息
    stats = generator.get_statistics()
    print("\n数据统计信息:")
    print(f"姓氏数量: {stats['surnames_count']}")
    print(f"男性用字数量: {stats['male_chars_count']}")
    print(f"女性用字数量: {stats['female_chars_count']}")
    print(f"男性姓名组合数: {stats['total_combinations_male']:,}")
    print(f"女性姓名组合数: {stats['total_combinations_female']:,}")
    
    # 生成单个姓名示例
    print("\n单个姓名生成示例:")
    print("-" * 20)
    
    for i in range(5):
        male_name = generator.generate_name('male')
        female_name = generator.generate_name('female')
        print(f"男性姓名 {i+1}: {male_name}")
        print(f"女性姓名 {i+1}: {female_name}")
    
    # 批量生成示例
    print("\n批量生成示例:")
    print("-" * 20)
    
    # 生成100个姓名，男女比例6:4
    names = generator.generate_batch(count=100, gender_ratio=0.6)

    # 分析生成的姓名
    analysis = utils.analyze_names(names)
    report = utils.format_analysis_report(analysis)
    print(report)
    
    # 显示前10个生成的姓名
    print("\n前10个生成的姓名:")
    print("-" * 20)
    for i, name_data in enumerate(names[:10], 1):
        gender_text = '男' if name_data['gender'] == 'male' else '女'
        print(f"{i:2d}. {name_data['name']} ({gender_text})")
    
    # 唯一性估算
    print("\n唯一性估算:")
    print("-" * 20)
    
    for count in [1000, 10000, 100000, 1000000]:
        uniqueness = generator.estimate_uniqueness(count)
        print(f"生成 {count:,} 个姓名:")
        print(f"  预估整体唯一性: {uniqueness['overall_uniqueness_ratio']:.1%}")
        print(f"  男性唯一性: {uniqueness['male_uniqueness_ratio']:.1%}")
        print(f"  女性唯一性: {uniqueness['female_uniqueness_ratio']:.1%}")
        print()


if __name__ == "__main__":
    main()
