"""
最简单的IDE测试脚本

专门为IDE环境设计，解决所有导入问题
"""

# 第一步：设置路径
import os
import sys

# 获取当前脚本的绝对路径
script_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(script_dir, 'src')

# 确保src目录在Python路径中
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

print(f"脚本目录: {script_dir}")
print(f"src目录: {src_dir}")
print(f"当前工作目录: {os.getcwd()}")

# 第二步：导入模块
try:
    from name_generator import ChineseNameGenerator
    from utils import export_to_csv, analyze_names
    print("✅ 所有模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print("请确保您在项目根目录运行此脚本")
    sys.exit(1)

def main():
    """主测试函数"""
    print("\n🎯 开始功能测试")
    print("=" * 30)
    
    # 创建生成器
    generator = ChineseNameGenerator()
    print("✅ 生成器创建成功")
    
    # 生成几个姓名
    print("\n📝 生成测试姓名:")
    for i in range(5):
        male = generator.generate_name('male')
        female = generator.generate_name('female')
        print(f"  {i+1}. 男: {male} | 女: {female}")
    
    # 批量生成
    print("\n📦 批量生成测试:")
    names = generator.generate_batch(100, 0.5)
    print(f"  生成了 {len(names)} 个姓名")
    
    # 导出测试
    print("\n💾 导出测试:")
    try:
        export_to_csv(names, "simple_test_output.csv")
        print("  ✅ CSV导出成功")
    except Exception as e:
        print(f"  ❌ 导出失败: {e}")
    
    # 分析测试
    print("\n📊 分析测试:")
    try:
        analysis = analyze_names(names)
        print(f"  男性: {analysis['male_count']}")
        print(f"  女性: {analysis['female_count']}")
        print(f"  不同姓氏: {analysis['unique_surnames']}")
        print("  ✅ 分析功能正常")
    except Exception as e:
        print(f"  ❌ 分析失败: {e}")
    
    # 性能测试
    print("\n⚡ 性能测试:")
    try:
        import time
        start = time.time()
        big_batch = generator.generate_batch(1000, 0.5)
        duration = time.time() - start
        speed = 1000 / duration
        print(f"  生成1000个姓名耗时: {duration:.2f}秒")
        print(f"  生成速度: {speed:,.0f} 个/秒")
        print("  ✅ 性能测试正常")
    except Exception as e:
        print(f"  ❌ 性能测试失败: {e}")
    
    print("\n🎉 所有测试完成！")
    
    # 检查生成的文件
    output_file = "simple_test_output.csv"
    if os.path.exists(output_file):
        print(f"\n📄 生成的文件: {output_file}")
        print(f"   文件大小: {os.path.getsize(output_file)} 字节")

if __name__ == "__main__":
    main()
