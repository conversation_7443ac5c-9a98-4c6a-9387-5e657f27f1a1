"""
中文姓名生成器核心模块

基于真实人口统计数据的中文姓名生成器
"""

import random
import numpy as np
from typing import List, Dict, Tuple, Optional
try:
    from .data_loader import DataLoader
except ImportError:
    from data_loader import DataLoader


class ChineseNameGenerator:
    """中文姓名生成器"""
    
    def __init__(self, data_dir: str = None, random_seed: Optional[int] = None):
        """
        初始化姓名生成器
        
        Args:
            data_dir: 数据文件目录路径
            random_seed: 随机种子，用于可重现的结果
        """
        if random_seed is not None:
            random.seed(random_seed)
            np.random.seed(random_seed)
        
        self.data_loader = DataLoader(data_dir)
        
        # 加载数据
        self.surnames, self.surname_weights = self.data_loader.load_surnames()
        self.male_chars, self.male_weights = self.data_loader.load_male_names()
        self.female_chars, self.female_weights = self.data_loader.load_female_names()
        
        # 验证数据
        if not self.data_loader.validate_data():
            raise ValueError("数据验证失败，请检查数据文件")
        
        # 转换为numpy数组以提高性能
        self.surname_weights = np.array(self.surname_weights)
        self.male_weights = np.array(self.male_weights)
        self.female_weights = np.array(self.female_weights)
    
    def _select_surname(self) -> str:
        """
        根据权重随机选择姓氏
        
        Returns:
            str: 选中的姓氏
        """
        return np.random.choice(self.surnames, p=self.surname_weights)
    
    def _select_given_name(self, gender: str, name_length: int = 2) -> str:
        """
        根据性别和权重选择名字
        
        Args:
            gender: 性别 ('male' 或 'female')
            name_length: 名字长度（1或2）
            
        Returns:
            str: 生成的名字
        """
        if gender.lower() == 'male':
            chars = self.male_chars
            weights = self.male_weights
        elif gender.lower() == 'female':
            chars = self.female_chars
            weights = self.female_weights
        else:
            raise ValueError("性别必须是 'male' 或 'female'")
        
        # 根据名字长度选择字符
        if name_length == 1:
            return np.random.choice(chars, p=weights)
        elif name_length == 2:
            # 选择两个字符组成名字
            char1 = np.random.choice(chars, p=weights)
            char2 = np.random.choice(chars, p=weights)
            return char1 + char2
        else:
            raise ValueError("名字长度必须是1或2")
    
    def generate_name(self, gender: str, name_length: int = None) -> str:
        """
        生成单个中文姓名
        
        Args:
            gender: 性别 ('male' 或 'female')
            name_length: 名字长度，None表示随机选择1或2
            
        Returns:
            str: 完整的中文姓名
        """
        if name_length is None:
            # 随机选择名字长度，2字名字更常见
            name_length = np.random.choice([1, 2], p=[0.2, 0.8])
        
        surname = self._select_surname()
        given_name = self._select_given_name(gender, name_length)
        
        return surname + given_name
    
    def generate_batch(self, count: int, gender_ratio: float = 0.5, 
                      name_length: int = None) -> List[Dict[str, str]]:
        """
        批量生成中文姓名
        
        Args:
            count: 生成数量
            gender_ratio: 男性比例（0-1之间），0.5表示男女各半
            name_length: 名字长度，None表示随机选择
            
        Returns:
            List[Dict[str, str]]: 包含姓名和性别信息的字典列表
        """
        if not 0 <= gender_ratio <= 1:
            raise ValueError("gender_ratio必须在0-1之间")
        
        results = []
        male_count = int(count * gender_ratio)
        female_count = count - male_count
        
        # 生成男性姓名
        for _ in range(male_count):
            name = self.generate_name('male', name_length)
            results.append({
                'name': name,
                'gender': 'male'
            })
        
        # 生成女性姓名
        for _ in range(female_count):
            name = self.generate_name('female', name_length)
            results.append({
                'name': name,
                'gender': 'female'
            })
        
        # 随机打乱顺序
        random.shuffle(results)
        
        return results
    
    def get_statistics(self) -> Dict[str, int]:
        """
        获取生成器统计信息
        
        Returns:
            Dict[str, int]: 统计信息
        """
        return self.data_loader.get_data_stats()
    
    def estimate_uniqueness(self, count: int, name_length: int = 2) -> Dict[str, float]:
        """
        估算指定数量下的姓名唯一性
        
        Args:
            count: 生成数量
            name_length: 名字长度
            
        Returns:
            Dict[str, float]: 唯一性估算结果
        """
        stats = self.get_statistics()
        
        if name_length == 1:
            total_male_combinations = stats['surnames_count'] * stats['male_chars_count']
            total_female_combinations = stats['surnames_count'] * stats['female_chars_count']
        else:  # name_length == 2
            total_male_combinations = stats['total_combinations_male']
            total_female_combinations = stats['total_combinations_female']
        
        # 简单估算（假设均匀分布）
        male_count = count // 2
        female_count = count - male_count
        
        male_uniqueness = min(1.0, male_count / total_male_combinations)
        female_uniqueness = min(1.0, female_count / total_female_combinations)
        overall_uniqueness = min(1.0, count / (total_male_combinations + total_female_combinations))
        
        return {
            'male_uniqueness_ratio': male_uniqueness,
            'female_uniqueness_ratio': female_uniqueness,
            'overall_uniqueness_ratio': overall_uniqueness,
            'total_male_combinations': total_male_combinations,
            'total_female_combinations': total_female_combinations
        }
