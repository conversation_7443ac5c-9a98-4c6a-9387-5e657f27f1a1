"""
数据加载模块

负责加载和预处理姓氏、男性名字、女性名字的数据文件
"""

import json
import os
import numpy as np
from typing import Dict, List, Tuple


class DataLoader:
    """数据加载器类"""
    
    def __init__(self, data_dir: str = None):
        """
        初始化数据加载器
        
        Args:
            data_dir: 数据文件目录路径，默认为当前模块的data目录
        """
        if data_dir is None:
            # 获取当前文件所在目录的data子目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            data_dir = os.path.join(current_dir, 'data')
        
        self.data_dir = data_dir
        self._surnames_data = None
        self._male_names_data = None
        self._female_names_data = None
    
    def load_surnames(self) -> Tuple[List[str], List[float]]:
        """
        加载姓氏数据
        
        Returns:
            Tuple[List[str], List[float]]: (姓氏列表, 频率列表)
        """
        if self._surnames_data is None:
            file_path = os.path.join(self.data_dir, 'surnames.json')
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            surnames = [item['surname'] for item in data['data']]
            frequencies = [item['frequency'] for item in data['data']]
            
            # 归一化频率，确保总和为1
            total_freq = sum(frequencies)
            frequencies = [freq / total_freq for freq in frequencies]
            
            self._surnames_data = (surnames, frequencies)
        
        return self._surnames_data
    
    def load_male_names(self) -> Tuple[List[str], List[float]]:
        """
        加载男性名字用字数据
        
        Returns:
            Tuple[List[str], List[float]]: (字符列表, 频率列表)
        """
        if self._male_names_data is None:
            file_path = os.path.join(self.data_dir, 'male_names.json')
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            chars = [item['char'] for item in data['data']]
            frequencies = [item['frequency'] for item in data['data']]
            
            # 归一化频率
            total_freq = sum(frequencies)
            frequencies = [freq / total_freq for freq in frequencies]
            
            self._male_names_data = (chars, frequencies)
        
        return self._male_names_data
    
    def load_female_names(self) -> Tuple[List[str], List[float]]:
        """
        加载女性名字用字数据
        
        Returns:
            Tuple[List[str], List[float]]: (字符列表, 频率列表)
        """
        if self._female_names_data is None:
            file_path = os.path.join(self.data_dir, 'female_names.json')
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            chars = [item['char'] for item in data['data']]
            frequencies = [item['frequency'] for item in data['data']]
            
            # 归一化频率
            total_freq = sum(frequencies)
            frequencies = [freq / total_freq for freq in frequencies]
            
            self._female_names_data = (chars, frequencies)
        
        return self._female_names_data
    
    def get_data_stats(self) -> Dict[str, int]:
        """
        获取数据统计信息
        
        Returns:
            Dict[str, int]: 包含各类数据数量的字典
        """
        surnames, _ = self.load_surnames()
        male_chars, _ = self.load_male_names()
        female_chars, _ = self.load_female_names()
        
        return {
            'surnames_count': len(surnames),
            'male_chars_count': len(male_chars),
            'female_chars_count': len(female_chars),
            'total_combinations_male': len(surnames) * len(male_chars) * len(male_chars),
            'total_combinations_female': len(surnames) * len(female_chars) * len(female_chars)
        }
    
    def validate_data(self) -> bool:
        """
        验证数据完整性
        
        Returns:
            bool: 数据是否有效
        """
        try:
            surnames, surname_freqs = self.load_surnames()
            male_chars, male_freqs = self.load_male_names()
            female_chars, female_freqs = self.load_female_names()
            
            # 检查数据是否为空
            if not surnames or not male_chars or not female_chars:
                return False
            
            # 检查频率总和是否接近1
            if abs(sum(surname_freqs) - 1.0) > 0.01:
                return False
            if abs(sum(male_freqs) - 1.0) > 0.01:
                return False
            if abs(sum(female_freqs) - 1.0) > 0.01:
                return False
            
            return True
            
        except Exception as e:
            print(f"数据验证失败: {e}")
            return False
