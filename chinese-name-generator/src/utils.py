"""
工具函数模块

提供姓名导出、格式化等辅助功能
"""

import csv
import json
import os
from typing import List, Dict, Optional
# import pandas as pd  # 暂时注释掉pandas以避免兼容性问题


def export_to_csv(names_data: List[Dict[str, str]], filename: str, 
                  include_index: bool = True) -> None:
    """
    将姓名数据导出为CSV文件
    
    Args:
        names_data: 姓名数据列表，每个元素包含name和gender字段
        filename: 输出文件名
        include_index: 是否包含序号列
    """
    # 确保文件扩展名为.csv
    if not filename.endswith('.csv'):
        filename += '.csv'
    
    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        fieldnames = ['序号', '姓名', '性别'] if include_index else ['姓名', '性别']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for i, data in enumerate(names_data, 1):
            row = {
                '姓名': data['name'],
                '性别': '男' if data['gender'] == 'male' else '女'
            }
            if include_index:
                row['序号'] = i
            writer.writerow(row)
    
    print(f"已导出 {len(names_data)} 个姓名到文件: {filename}")


def export_to_txt(names_data: List[Dict[str, str]], filename: str,
                  format_style: str = 'simple') -> None:
    """
    将姓名数据导出为TXT文件
    
    Args:
        names_data: 姓名数据列表
        filename: 输出文件名
        format_style: 格式样式 ('simple', 'detailed', 'list')
    """
    # 确保文件扩展名为.txt
    if not filename.endswith('.txt'):
        filename += '.txt'
    
    with open(filename, 'w', encoding='utf-8') as txtfile:
        if format_style == 'simple':
            # 简单格式：每行一个姓名
            for data in names_data:
                txtfile.write(f"{data['name']}\n")
        
        elif format_style == 'detailed':
            # 详细格式：包含性别信息
            txtfile.write("中文姓名列表\n")
            txtfile.write("=" * 30 + "\n\n")
            for i, data in enumerate(names_data, 1):
                gender_text = '男' if data['gender'] == 'male' else '女'
                txtfile.write(f"{i:6d}. {data['name']} ({gender_text})\n")
        
        elif format_style == 'list':
            # 列表格式：按性别分组
            male_names = [data['name'] for data in names_data if data['gender'] == 'male']
            female_names = [data['name'] for data in names_data if data['gender'] == 'female']
            
            txtfile.write("中文姓名列表\n")
            txtfile.write("=" * 30 + "\n\n")
            
            txtfile.write(f"男性姓名 ({len(male_names)}个):\n")
            txtfile.write("-" * 20 + "\n")
            for name in male_names:
                txtfile.write(f"{name}\n")
            
            txtfile.write(f"\n女性姓名 ({len(female_names)}个):\n")
            txtfile.write("-" * 20 + "\n")
            for name in female_names:
                txtfile.write(f"{name}\n")
    
    print(f"已导出 {len(names_data)} 个姓名到文件: {filename}")


def export_to_json(names_data: List[Dict[str, str]], filename: str,
                   include_metadata: bool = True) -> None:
    """
    将姓名数据导出为JSON文件
    
    Args:
        names_data: 姓名数据列表
        filename: 输出文件名
        include_metadata: 是否包含元数据
    """
    # 确保文件扩展名为.json
    if not filename.endswith('.json'):
        filename += '.json'
    
    output_data = {
        'names': names_data
    }
    
    if include_metadata:
        male_count = sum(1 for data in names_data if data['gender'] == 'male')
        female_count = len(names_data) - male_count
        
        output_data['metadata'] = {
            'total_count': len(names_data),
            'male_count': male_count,
            'female_count': female_count,
            'male_ratio': male_count / len(names_data) if names_data else 0,
            'female_ratio': female_count / len(names_data) if names_data else 0
        }
    
    with open(filename, 'w', encoding='utf-8') as jsonfile:
        json.dump(output_data, jsonfile, ensure_ascii=False, indent=2)
    
    print(f"已导出 {len(names_data)} 个姓名到文件: {filename}")


def analyze_names(names_data: List[Dict[str, str]]) -> Dict[str, any]:
    """
    分析姓名数据的统计信息
    
    Args:
        names_data: 姓名数据列表
        
    Returns:
        Dict[str, any]: 统计分析结果
    """
    if not names_data:
        return {}
    
    # 基础统计
    total_count = len(names_data)
    male_count = sum(1 for data in names_data if data['gender'] == 'male')
    female_count = total_count - male_count
    
    # 姓氏统计
    surnames = {}
    name_lengths = {}
    
    for data in names_data:
        name = data['name']
        surname = name[0]  # 第一个字符为姓氏
        name_length = len(name)
        
        surnames[surname] = surnames.get(surname, 0) + 1
        name_lengths[name_length] = name_lengths.get(name_length, 0) + 1
    
    # 最常见的姓氏（前10个）
    top_surnames = sorted(surnames.items(), key=lambda x: x[1], reverse=True)[:10]
    
    return {
        'total_count': total_count,
        'male_count': male_count,
        'female_count': female_count,
        'male_ratio': male_count / total_count,
        'female_ratio': female_count / total_count,
        'unique_surnames': len(surnames),
        'top_surnames': top_surnames,
        'name_length_distribution': name_lengths,
        'average_name_length': sum(len(data['name']) for data in names_data) / total_count
    }


def format_analysis_report(analysis: Dict[str, any]) -> str:
    """
    格式化分析报告
    
    Args:
        analysis: 分析结果字典
        
    Returns:
        str: 格式化的报告文本
    """
    if not analysis:
        return "无数据可分析"
    
    report = []
    report.append("姓名数据分析报告")
    report.append("=" * 30)
    report.append("")
    
    # 基础统计
    report.append("基础统计:")
    report.append(f"  总数量: {analysis['total_count']:,}")
    report.append(f"  男性: {analysis['male_count']:,} ({analysis['male_ratio']:.1%})")
    report.append(f"  女性: {analysis['female_count']:,} ({analysis['female_ratio']:.1%})")
    report.append("")
    
    # 姓氏统计
    report.append("姓氏统计:")
    report.append(f"  不同姓氏数量: {analysis['unique_surnames']}")
    report.append("  最常见姓氏:")
    for surname, count in analysis['top_surnames']:
        percentage = count / analysis['total_count'] * 100
        report.append(f"    {surname}: {count:,} ({percentage:.1f}%)")
    report.append("")
    
    # 名字长度分布
    report.append("名字长度分布:")
    for length, count in sorted(analysis['name_length_distribution'].items()):
        percentage = count / analysis['total_count'] * 100
        report.append(f"  {length}字: {count:,} ({percentage:.1f}%)")
    report.append(f"  平均长度: {analysis['average_name_length']:.1f}字")
    
    return "\n".join(report)
