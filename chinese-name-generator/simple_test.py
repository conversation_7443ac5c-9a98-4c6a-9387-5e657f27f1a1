"""
简单测试脚本 - 验证模块重命名后是否正常工作
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 导入重命名后的模块
import name_generator

def main():
    print("🧪 测试重命名后的模块")
    print("=" * 30)
    
    try:
        # 创建生成器
        generator = name_generator.ChineseNameGenerator()
        print("✅ 模块导入成功")
        
        # 生成几个姓名测试
        print("\n生成测试姓名:")
        for i in range(3):
            male_name = generator.generate_name('male')
            female_name = generator.generate_name('female')
            print(f"  {i+1}. 男: {male_name} | 女: {female_name}")
        
        # 批量生成测试
        names = generator.generate_batch(10, 0.5)
        print(f"\n✅ 批量生成成功: {len(names)} 个姓名")
        
        print("\n🎉 所有测试通过！模块重命名成功！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
