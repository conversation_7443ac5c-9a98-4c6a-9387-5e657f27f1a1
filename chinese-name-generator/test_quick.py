"""
快速测试脚本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import generator
import utils

def main():
    print("快速测试中文姓名生成器")
    print("=" * 30)
    
    # 创建生成器
    name_generator = generator.ChineseNameGenerator()
    
    # 生成1000个姓名
    print("正在生成1000个姓名...")
    names = name_generator.generate_batch(1000, 0.5)
    
    # 导出CSV
    utils.export_to_csv(names, "test_1000_names.csv")
    
    # 分析
    analysis = utils.analyze_names(names)
    print(f"\n生成完成！")
    print(f"总数: {analysis['total_count']}")
    print(f"男性: {analysis['male_count']} ({analysis['male_ratio']:.1%})")
    print(f"女性: {analysis['female_count']} ({analysis['female_ratio']:.1%})")
    print(f"不同姓氏: {analysis['unique_surnames']}")
    
    # 显示前10个
    print("\n前10个姓名:")
    for i, name_data in enumerate(names[:10], 1):
        gender_text = '男' if name_data['gender'] == 'male' else '女'
        print(f"{i:2d}. {name_data['name']} ({gender_text})")
    
    print(f"\n已导出到文件: test_1000_names.csv")

if __name__ == "__main__":
    main()
