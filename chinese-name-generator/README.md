# 中文姓名生成器

基于真实人口统计数据的中文姓名生成器，支持按需生成大量高质量中文姓名。

## 功能特点

- 🎯 基于真实人口统计数据的权重分布
- 👫 支持男女姓名区分
- ⚡ 高性能：每秒可生成约10万个姓名
- 🔄 按需生成，无重复限制
- 📊 支持批量生成和统计分析

## 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 基础使用

```python
from src.name_generator import ChineseNameGenerator

# 创建生成器实例
generator = ChineseNameGenerator()

# 生成单个男性姓名
male_name = generator.generate_name(gender='male')
print(f"男性姓名: {male_name}")

# 生成单个女性姓名
female_name = generator.generate_name(gender='female')
print(f"女性姓名: {female_name}")

# 批量生成（男女比例1:1）
names = generator.generate_batch(count=1000, gender_ratio=0.5)
print(f"生成了 {len(names)} 个姓名")
```

### 批量生成示例

```python
# 生成100万个姓名
names = generator.generate_batch(count=1000000, gender_ratio=0.52)

# 导出到文件
generator.export_to_csv(names, 'names_1million.csv')
```

## 项目结构

```
chinese-name-generator/
├── README.md                     # 项目说明
├── requirements.txt              # 依赖包
├── src/                         # 源代码
│   ├── __init__.py
│   ├── data/                    # 数据文件
│   │   ├── surnames.json        # 姓氏数据及频率
│   │   ├── male_names.json      # 男性名字用字及频率
│   │   └── female_names.json    # 女性名字用字及频率
│   ├── name_generator.py        # 核心生成器类
│   ├── data_loader.py          # 数据加载模块
│   └── utils.py                # 工具函数
├── examples/                    # 使用示例
│   ├── basic_usage.py          # 基础使用示例
│   └── batch_generation.py     # 批量生成示例
└── tests/                      # 单元测试
    └── test_generator.py       # 生成器测试
```

## 数据来源

- 姓氏数据：基于中国人口普查数据的百家姓分布
- 名字用字：基于现代汉语常用字及其在姓名中的使用频率
- 性别区分：根据男女姓名用字的统计差异

## 性能指标

- 内存占用：约50MB（数据加载后）
- 生成速度：每秒约10万个姓名
- 数据规模：400+姓氏，1600+常用名字用字

## 许可证

MIT License
