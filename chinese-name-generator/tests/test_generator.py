"""
中文姓名生成器单元测试
"""

import unittest
import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from generator import ChineseNameGenerator
from data_loader import DataLoader
from utils import analyze_names, export_to_csv, export_to_txt


class TestDataLoader(unittest.TestCase):
    """数据加载器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.data_loader = DataLoader()
    
    def test_load_surnames(self):
        """测试姓氏数据加载"""
        surnames, frequencies = self.data_loader.load_surnames()
        
        self.assertIsInstance(surnames, list)
        self.assertIsInstance(frequencies, list)
        self.assertEqual(len(surnames), len(frequencies))
        self.assertGreater(len(surnames), 0)
        
        # 检查频率总和接近1
        self.assertAlmostEqual(sum(frequencies), 1.0, places=2)
        
        # 检查所有频率都是正数
        for freq in frequencies:
            self.assertGreater(freq, 0)
    
    def test_load_male_names(self):
        """测试男性名字数据加载"""
        chars, frequencies = self.data_loader.load_male_names()
        
        self.assertIsInstance(chars, list)
        self.assertIsInstance(frequencies, list)
        self.assertEqual(len(chars), len(frequencies))
        self.assertGreater(len(chars), 0)
        self.assertAlmostEqual(sum(frequencies), 1.0, places=2)
    
    def test_load_female_names(self):
        """测试女性名字数据加载"""
        chars, frequencies = self.data_loader.load_female_names()
        
        self.assertIsInstance(chars, list)
        self.assertIsInstance(frequencies, list)
        self.assertEqual(len(chars), len(frequencies))
        self.assertGreater(len(chars), 0)
        self.assertAlmostEqual(sum(frequencies), 1.0, places=2)
    
    def test_validate_data(self):
        """测试数据验证"""
        self.assertTrue(self.data_loader.validate_data())
    
    def test_get_data_stats(self):
        """测试数据统计"""
        stats = self.data_loader.get_data_stats()
        
        self.assertIn('surnames_count', stats)
        self.assertIn('male_chars_count', stats)
        self.assertIn('female_chars_count', stats)
        self.assertIn('total_combinations_male', stats)
        self.assertIn('total_combinations_female', stats)
        
        self.assertGreater(stats['surnames_count'], 0)
        self.assertGreater(stats['male_chars_count'], 0)
        self.assertGreater(stats['female_chars_count'], 0)


class TestChineseNameGenerator(unittest.TestCase):
    """中文姓名生成器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.generator = ChineseNameGenerator(random_seed=42)
    
    def test_generate_male_name(self):
        """测试生成男性姓名"""
        name = self.generator.generate_name('male')
        
        self.assertIsInstance(name, str)
        self.assertGreaterEqual(len(name), 2)  # 至少2个字符（姓+名）
        self.assertLessEqual(len(name), 3)     # 最多3个字符
    
    def test_generate_female_name(self):
        """测试生成女性姓名"""
        name = self.generator.generate_name('female')
        
        self.assertIsInstance(name, str)
        self.assertGreaterEqual(len(name), 2)
        self.assertLessEqual(len(name), 3)
    
    def test_generate_name_with_length(self):
        """测试指定长度的姓名生成"""
        # 测试1字名
        name1 = self.generator.generate_name('male', name_length=1)
        self.assertEqual(len(name1), 2)  # 姓(1) + 名(1)
        
        # 测试2字名
        name2 = self.generator.generate_name('female', name_length=2)
        self.assertEqual(len(name2), 3)  # 姓(1) + 名(2)
    
    def test_invalid_gender(self):
        """测试无效性别参数"""
        with self.assertRaises(ValueError):
            self.generator.generate_name('invalid')
    
    def test_invalid_name_length(self):
        """测试无效名字长度"""
        with self.assertRaises(ValueError):
            self.generator.generate_name('male', name_length=3)
    
    def test_generate_batch(self):
        """测试批量生成"""
        count = 100
        gender_ratio = 0.6
        names = self.generator.generate_batch(count, gender_ratio)
        
        self.assertEqual(len(names), count)
        
        # 检查数据结构
        for name_data in names:
            self.assertIn('name', name_data)
            self.assertIn('gender', name_data)
            self.assertIn(name_data['gender'], ['male', 'female'])
            self.assertIsInstance(name_data['name'], str)
        
        # 检查性别比例（允许一定误差）
        male_count = sum(1 for data in names if data['gender'] == 'male')
        actual_ratio = male_count / count
        self.assertAlmostEqual(actual_ratio, gender_ratio, delta=0.1)
    
    def test_invalid_gender_ratio(self):
        """测试无效性别比例"""
        with self.assertRaises(ValueError):
            self.generator.generate_batch(100, gender_ratio=1.5)
        
        with self.assertRaises(ValueError):
            self.generator.generate_batch(100, gender_ratio=-0.1)
    
    def test_get_statistics(self):
        """测试统计信息获取"""
        stats = self.generator.get_statistics()
        
        self.assertIsInstance(stats, dict)
        self.assertIn('surnames_count', stats)
        self.assertIn('male_chars_count', stats)
        self.assertIn('female_chars_count', stats)
    
    def test_estimate_uniqueness(self):
        """测试唯一性估算"""
        uniqueness = self.generator.estimate_uniqueness(1000)
        
        self.assertIsInstance(uniqueness, dict)
        self.assertIn('male_uniqueness_ratio', uniqueness)
        self.assertIn('female_uniqueness_ratio', uniqueness)
        self.assertIn('overall_uniqueness_ratio', uniqueness)
        
        # 检查比例在合理范围内
        for key in ['male_uniqueness_ratio', 'female_uniqueness_ratio', 'overall_uniqueness_ratio']:
            self.assertGreaterEqual(uniqueness[key], 0)
            self.assertLessEqual(uniqueness[key], 1)
    
    def test_reproducibility(self):
        """测试可重现性（相同种子应产生相同结果）"""
        generator1 = ChineseNameGenerator(random_seed=123)
        generator2 = ChineseNameGenerator(random_seed=123)
        
        names1 = generator1.generate_batch(10, 0.5)
        names2 = generator2.generate_batch(10, 0.5)
        
        # 应该生成相同的姓名序列
        for i in range(10):
            self.assertEqual(names1[i]['name'], names2[i]['name'])
            self.assertEqual(names1[i]['gender'], names2[i]['gender'])


class TestUtils(unittest.TestCase):
    """工具函数测试"""
    
    def setUp(self):
        """测试前准备"""
        self.generator = ChineseNameGenerator(random_seed=42)
        self.test_names = self.generator.generate_batch(50, 0.5)
    
    def test_analyze_names(self):
        """测试姓名分析"""
        analysis = analyze_names(self.test_names)
        
        self.assertIsInstance(analysis, dict)
        self.assertEqual(analysis['total_count'], 50)
        self.assertIn('male_count', analysis)
        self.assertIn('female_count', analysis)
        self.assertIn('unique_surnames', analysis)
        self.assertIn('top_surnames', analysis)
        
        # 检查男女总数
        self.assertEqual(
            analysis['male_count'] + analysis['female_count'],
            analysis['total_count']
        )
    
    def test_export_functions(self):
        """测试导出功能"""
        # 测试CSV导出
        csv_file = 'test_names.csv'
        export_to_csv(self.test_names, csv_file)
        self.assertTrue(os.path.exists(csv_file))
        
        # 测试TXT导出
        txt_file = 'test_names.txt'
        export_to_txt(self.test_names, txt_file)
        self.assertTrue(os.path.exists(txt_file))
        
        # 清理测试文件
        if os.path.exists(csv_file):
            os.remove(csv_file)
        if os.path.exists(txt_file):
            os.remove(txt_file)


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
