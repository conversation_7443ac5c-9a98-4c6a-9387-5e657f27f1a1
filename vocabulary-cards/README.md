# Vocabulary Cards

这是一个用于展示英语词汇学习卡片的项目。每个卡片包含单词的发音、释义、同义词和例句等信息。

## 在Markdown中展示SVG图像的方法

有以下几种方法可以在Markdown中展示SVG图像：

1. 直接引用SVG文件：
```markdown
![词汇卡片](./images/appointee.svg)
```

2. 使用img标签：
```html
<img src="./images/appointee.svg" alt="词汇卡片" width="600"/>
```

3. 内嵌SVG代码：
可以直接将SVG代码嵌入到Markdown文件中：
```html
<svg width="600" height="720" xmlns="http://www.w3.org/2000/svg">
  <!-- SVG内容 -->
</svg>
```

4. 使用Base64编码：
```markdown
![词汇卡片](data:image/svg+xml;base64,编码后的SVG内容)
```

## 项目结构

```
vocabulary-cards/
├── README.md
├── images/
│   └── appointee.svg
└── cards/
    └── appointee.md
```

## 使用说明

1. 所有SVG图像文件存放在`images`目录下
2. 每个单词的详细笔记存放在`cards`目录下
3. 图片命名规则：使用单词本身作为文件名，如`appointee.svg`
4. Markdown文件命名规则：与对应的SVG图片保持一致，如`appointee.md`

## 注意事项

1. SVG文件应当控制在合适的大小，建议宽度不超过800px
2. 使用Base64编码时，需要确保编码内容正确，不含有特殊字符
3. 建议使用相对路径引用图片，以确保项目在不同环境下都能正常显示 