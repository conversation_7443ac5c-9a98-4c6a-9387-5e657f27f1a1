# 中文姓名生成器项目

## 项目概述
创建一个基于真实人口统计数据的中文姓名生成器，支持按需生成14亿人的姓名，区分男女姓名。

## 实现计划

### 项目结构
```
chinese-name-generator/
├── README.md
├── requirements.txt
├── src/
│   ├── __init__.py
│   ├── data/
│   │   ├── surnames.json          # 姓氏数据及频率
│   │   ├── male_names.json        # 男性名字用字及频率
│   │   └── female_names.json      # 女性名字用字及频率
│   ├── generator.py               # 核心生成器类
│   ├── data_loader.py            # 数据加载模块
│   └── utils.py                  # 工具函数
├── examples/
│   ├── basic_usage.py            # 基础使用示例
│   └── batch_generation.py       # 批量生成示例
└── tests/
    └── test_generator.py         # 单元测试
```

### 技术要点
- 使用numpy.random.choice进行权重随机选择
- JSON格式存储统计数据
- 面向对象设计，便于扩展
- 预期性能：每秒约10万个姓名

## 执行状态
- [x] 项目规划
- [ ] 项目初始化
- [ ] 数据准备
- [ ] 核心生成器实现
- [ ] 数据加载模块
- [ ] 工具函数
- [ ] 示例和测试
