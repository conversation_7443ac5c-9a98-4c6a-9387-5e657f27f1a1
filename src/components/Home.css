.home-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.home-container h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 2rem;
  text-align: center;
}

.game-modes {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3rem;
  width: 100%;
}

.mode-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 60px;
  background-color: #4a90e2;
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.mode-button:hover {
  background-color: #357abD;
  transform: translateY(-3px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.game-info {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.game-info h2 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1rem;
}

.game-info p {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 1rem;
}

.game-info ul {
  padding-left: 1.5rem;
}

.game-info li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
  color: #555;
}

.update-info {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #4a90e2;
}

.update-info p {
  color: #333;
  margin-bottom: 0.5rem;
}

.update-info ul {
  margin-top: 0.5rem;
}

.update-info li {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.3rem;
}

@media (max-width: 600px) {
  .game-modes {
    flex-direction: column;
    align-items: center;
  }
  
  .mode-button {
    width: 80%;
  }
} 