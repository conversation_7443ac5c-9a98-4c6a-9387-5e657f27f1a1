.game-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

.game-container h2 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 1rem;
}

.back-link {
  margin-bottom: 2rem;
  color: #4a90e2;
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.back-link:hover {
  text-decoration: underline;
}

.game-status {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  padding: 0.8rem 1.5rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  color: #333;
  text-align: center;
  width: 100%;
  max-width: 400px;
}

.game-board {
  margin-bottom: 2rem;
  width: 100%;
  display: flex;
  justify-content: center;
}

.reset-button {
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s;
}

.reset-button:hover {
  background-color: #388e3c;
}

/* 移动端优化 */
@media (max-width: 480px) {
  .game-container {
    padding: 0.5rem;
  }
  
  .game-container h2 {
    font-size: 1.5rem;
  }
  
  .game-status {
    font-size: 1rem;
    padding: 0.6rem 1rem;
    margin-bottom: 1rem;
  }
  
  .reset-button {
    padding: 0.6rem 1.2rem;
  }
}

/* 平板优化 */
@media (min-width: 481px) and (max-width: 768px) {
  .game-container h2 {
    font-size: 1.8rem;
  }
} 