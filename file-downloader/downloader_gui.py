import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import requests
import time
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
from datetime import datetime

# 尝试导入可选依赖
try:
    from ttkthemes import ThemedTk as TkRoot
    THEMED_TK_AVAILABLE = True
except ImportError:
    TkRoot = tk.Tk
    THEMED_TK_AVAILABLE = False

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

# 全局常量
DEFAULT_OUTPUT_DIR = "downloaded_files"
DEFAULT_PROXY = "http://localhost:1013"
DEFAULT_WORKERS = 5
DEFAULT_RETRIES = 3
PAGE_SIZE = 10  # 每页显示的图集数量
CONFIG_FILE = "downloader_config.json"

# 辅助函数
def extract_filename_from_url(url):
    """从URL中提取文件名"""
    parsed_url = urlparse(url)
    path = parsed_url.path
    return os.path.basename(path)

class DownloaderApp:
    """图集下载器GUI应用程序"""
    
    def __init__(self, root):
        """初始化应用程序"""
        self.root = root
        self.root.title("图集下载器")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        if THEMED_TK_AVAILABLE:
            self.root.set_theme("arc")  # 使用更现代的主题
        
        # 内部变量
        self.input_file_path = tk.StringVar()
        self.output_dir_path = tk.StringVar(value=os.path.abspath(DEFAULT_OUTPUT_DIR))
        self.use_proxy = tk.BooleanVar(value=False)
        self.proxy_address = tk.StringVar(value=DEFAULT_PROXY)
        self.workers_count = tk.IntVar(value=DEFAULT_WORKERS)
        self.retries_count = tk.IntVar(value=DEFAULT_RETRIES)
        
        self.title_groups = []  # 解析出的标题组列表 [(title, urls), ...]
        self.selected_groups = {}  # 选中的图集索引 {index: True/False}
        self.current_page = 0  # 当前页码
        self.total_pages = 0  # 总页数
        
        # 下载任务队列和结果队列
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # 下载状态
        self.is_downloading = False
        self.download_thread = None
        self.stop_event = threading.Event()
        
        # 创建界面
        self._create_widgets()
        self._arrange_widgets()
        
        # 添加选择事件处理
        self.album_tree.bind("<Button-1>", self._on_tree_click)
        
        # 加载配置
        self._load_config()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_widgets(self):
        """创建界面控件"""
        # 样式设置
        self.style = ttk.Style()
        
        # === 文件选择区域 ===
        self.file_frame = ttk.LabelFrame(self.root, text="文件选择")
        
        self.file_entry = ttk.Entry(self.file_frame, textvariable=self.input_file_path, width=50)
        self.file_btn = ttk.Button(self.file_frame, text="浏览", command=self._browse_file)
        self.parse_btn = ttk.Button(self.file_frame, text="解析文件", command=self._parse_file)
        
        # === 输出目录区域 ===
        self.output_frame = ttk.LabelFrame(self.root, text="保存位置")
        
        self.output_entry = ttk.Entry(self.output_frame, textvariable=self.output_dir_path, width=50)
        self.output_btn = ttk.Button(self.output_frame, text="浏览", command=self._browse_output_dir)
        
        # === 代理设置区域 ===
        self.proxy_frame = ttk.LabelFrame(self.root, text="代理设置")
        
        self.proxy_check = ttk.Checkbutton(self.proxy_frame, text="使用代理", variable=self.use_proxy)
        self.proxy_entry = ttk.Entry(self.proxy_frame, textvariable=self.proxy_address, width=30)
        
        # === 下载选项区域 ===
        self.options_frame = ttk.LabelFrame(self.root, text="下载选项")
        
        self.workers_label = ttk.Label(self.options_frame, text="并行下载数:")
        self.workers_spin = ttk.Spinbox(self.options_frame, from_=1, to=20, textvariable=self.workers_count, width=5)
        
        self.retries_label = ttk.Label(self.options_frame, text="重试次数:")
        self.retries_spin = ttk.Spinbox(self.options_frame, from_=0, to=10, textvariable=self.retries_count, width=5)
        
        # === 图集列表区域 ===
        self.list_frame = ttk.LabelFrame(self.root, text="可用图集")
        
        # 创建表格头部
        self.columns = ("select", "title", "count")
        self.album_tree = ttk.Treeview(self.list_frame, columns=self.columns, show="headings", selectmode="none")
        self.album_tree.heading("select", text="选择")
        self.album_tree.heading("title", text="标题")
        self.album_tree.heading("count", text="链接数量")
        
        self.album_tree.column("select", width=50, anchor="center")
        self.album_tree.column("title", width=400)
        self.album_tree.column("count", width=80, anchor="center")
        
        # 为表格添加滚动条
        self.album_scroll = ttk.Scrollbar(self.list_frame, orient="vertical", command=self.album_tree.yview)
        self.album_tree.configure(yscrollcommand=self.album_scroll.set)
        
        # 表格下方的分页控制区域
        self.paging_frame = ttk.Frame(self.list_frame)
        
        self.prev_page_btn = ttk.Button(self.paging_frame, text="上一页", command=self._prev_page, state="disabled")
        self.page_info_label = ttk.Label(self.paging_frame, text="0/0")
        self.next_page_btn = ttk.Button(self.paging_frame, text="下一页", command=self._next_page, state="disabled")
        
        self.select_all_btn = ttk.Button(self.paging_frame, text="全选", command=self._select_all)
        self.deselect_all_btn = ttk.Button(self.paging_frame, text="取消全选", command=self._deselect_all)
        
        # === 操作按钮区域 ===
        self.action_frame = ttk.Frame(self.root)
        
        self.download_btn = ttk.Button(self.action_frame, text="开始下载", command=self._start_download, state="disabled")
        self.stop_btn = ttk.Button(self.action_frame, text="停止下载", command=self._stop_download, state="disabled")
        
        # === 进度条区域 ===
        self.progress_frame = ttk.Frame(self.root)
        
        self.progress_label = ttk.Label(self.progress_frame, text="准备就绪")
        self.progress_bar = ttk.Progressbar(self.progress_frame, mode="determinate", length=700)
        
        # === 日志区域 ===
        self.log_frame = ttk.LabelFrame(self.root, text="下载日志")
        
        self.log_text = scrolledtext.ScrolledText(self.log_frame, height=10, wrap=tk.WORD)
        self.log_text.config(state="disabled")
        
        self.clear_log_btn = ttk.Button(self.log_frame, text="清除日志", command=self._clear_log)
    
    def _arrange_widgets(self):
        """布局控件"""
        # === 文件选择区域 ===
        self.file_frame.pack(fill="x", padx=10, pady=(10, 5))
        self.file_entry.pack(side="left", padx=5, pady=5)
        self.file_btn.pack(side="left", padx=5, pady=5)
        self.parse_btn.pack(side="left", padx=5, pady=5)
        
        # === 输出目录区域 ===
        self.output_frame.pack(fill="x", padx=10, pady=5)
        self.output_entry.pack(side="left", padx=5, pady=5)
        self.output_btn.pack(side="left", padx=5, pady=5)
        
        # === 代理设置区域 ===
        self.proxy_frame.pack(fill="x", padx=10, pady=5)
        self.proxy_check.pack(side="left", padx=5, pady=5)
        self.proxy_entry.pack(side="left", padx=5, pady=5)
        
        # === 下载选项区域 ===
        self.options_frame.pack(fill="x", padx=10, pady=5)
        self.workers_label.pack(side="left", padx=(5, 0), pady=5)
        self.workers_spin.pack(side="left", padx=(0, 10), pady=5)
        self.retries_label.pack(side="left", padx=(5, 0), pady=5)
        self.retries_spin.pack(side="left", padx=(0, 10), pady=5)
        
        # === 图集列表区域 ===
        self.list_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.album_tree.pack(side="left", fill="both", expand=True)
        self.album_scroll.pack(side="right", fill="y")
        
        self.paging_frame.pack(fill="x", pady=5)
        self.prev_page_btn.pack(side="left", padx=5)
        self.page_info_label.pack(side="left", padx=15)
        self.next_page_btn.pack(side="left", padx=5)
        
        self.select_all_btn.pack(side="right", padx=5)
        self.deselect_all_btn.pack(side="right", padx=5)
        
        # === 操作按钮区域 ===
        self.action_frame.pack(fill="x", padx=10, pady=5)
        self.download_btn.pack(side="left", padx=5)
        self.stop_btn.pack(side="left", padx=5)
        
        # === 进度条区域 ===
        self.progress_frame.pack(fill="x", padx=10, pady=5)
        self.progress_label.pack(anchor="w", padx=5, pady=(0, 5))
        self.progress_bar.pack(fill="x", padx=5, pady=(0, 5))
        
        # === 日志区域 ===
        self.log_frame.pack(fill="both", padx=10, pady=(5, 10), expand=True)
        self.log_text.pack(fill="both", expand=True, padx=5, pady=5)
        self.clear_log_btn.pack(anchor="e", padx=5, pady=(0, 5))
    
    def _browse_file(self):
        """浏览并选择输入文件"""
        file_path = filedialog.askopenfilename(
            title="选择链接文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if file_path:
            self.input_file_path.set(file_path)
            self._log(f"已选择文件: {file_path}")
    
    def _browse_output_dir(self):
        """浏览并选择输出目录"""
        dir_path = filedialog.askdirectory(
            title="选择保存目录"
        )
        if dir_path:
            self.output_dir_path.set(dir_path)
            self._log(f"已选择保存目录: {dir_path}")
    
    def _parse_file(self):
        """解析输入文件"""
        file_path = self.input_file_path.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择一个文件")
            return
        
        if not os.path.exists(file_path):
            messagebox.showerror("错误", f"文件不存在: {file_path}")
            return
        
        try:
            # 清空之前的解析结果
            self.title_groups = []
            self.selected_groups = {}
            
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as file:
                lines = [line.strip() for line in file.readlines() if line.strip()]
            
            # 解析标题组
            current_title = None
            current_urls = []
            
            for line in lines:
                # 如果行不是以http开头，则认为是标题行
                if not line.startswith('http'):
                    # 如果已经有标题，则将当前标题和URL保存
                    if current_title and current_urls:
                        self.title_groups.append((current_title, current_urls.copy()))
                    # 开始新的标题组
                    current_title = line
                    current_urls = []
                else:
                    # 如果是URL行，添加到当前标题的URL列表
                    current_urls.append(line)
            
            # 添加最后一个标题组
            if current_title and current_urls:
                self.title_groups.append((current_title, current_urls))
            
            # 更新图集列表
            self.current_page = 0
            self._update_album_list()
            
            # 更新日志
            total_groups = len(self.title_groups)
            total_urls = sum(len(urls) for _, urls in self.title_groups)
            self._log(f"解析完成，找到 {total_groups} 个图集，共 {total_urls} 个链接")
            
            # 如果有图集，启用下载按钮
            if self.title_groups:
                self.download_btn.config(state="normal")
            
        except Exception as e:
            messagebox.showerror("解析错误", str(e))
            self._log(f"解析文件出错: {str(e)}")
    
    def _update_album_list(self):
        """更新图集列表显示"""
        # 清空当前列表
        for item in self.album_tree.get_children():
            self.album_tree.delete(item)
        
        if not self.title_groups:
            # 没有图集，禁用分页按钮
            self.prev_page_btn.config(state="disabled")
            self.next_page_btn.config(state="disabled")
            self.page_info_label.config(text="0/0")
            return
        
        # 计算总页数
        total_groups = len(self.title_groups)
        self.total_pages = (total_groups - 1) // PAGE_SIZE + 1
        
        # 确保当前页有效
        if self.current_page >= self.total_pages:
            self.current_page = self.total_pages - 1
        elif self.current_page < 0:
            self.current_page = 0
        
        # 计算当前页的索引范围
        start_idx = self.current_page * PAGE_SIZE
        end_idx = min(start_idx + PAGE_SIZE, total_groups)
        
        # 添加当前页的图集项
        for idx in range(start_idx, end_idx):
            title, urls = self.title_groups[idx]
            is_selected = self.selected_groups.get(idx, False)
            select_text = "✓" if is_selected else ""
            
            self.album_tree.insert("", "end", iid=str(idx), values=(select_text, title, len(urls)))
        
        # 更新分页信息
        self.page_info_label.config(text=f"{self.current_page + 1}/{self.total_pages}")
        
        # 更新分页按钮状态
        self.prev_page_btn.config(state="normal" if self.current_page > 0 else "disabled")
        self.next_page_btn.config(state="normal" if self.current_page < self.total_pages - 1 else "disabled")
    
    def _on_tree_click(self, event):
        """处理表格点击事件"""
        region = self.album_tree.identify_region(event.x, event.y)
        if region == "cell":
            column = self.album_tree.identify_column(event.x)
            if column == "#1":  # 选择列
                item_id = self.album_tree.identify_row(event.y)
                if item_id:
                    idx = int(item_id)
                    # 切换选中状态
                    self.selected_groups[idx] = not self.selected_groups.get(idx, False)
                    # 更新显示
                    select_text = "✓" if self.selected_groups[idx] else ""
                    title, urls = self.title_groups[idx]
                    self.album_tree.item(item_id, values=(select_text, title, len(urls)))
                    
                    # 更新下载按钮状态
                    if any(self.selected_groups.values()):
                        self.download_btn.config(state="normal")
                    else:
                        self.download_btn.config(state="disabled")
    
    def _prev_page(self):
        """显示上一页图集"""
        if self.current_page > 0:
            self.current_page -= 1
            self._update_album_list()
    
    def _next_page(self):
        """显示下一页图集"""
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            self._update_album_list()
    
    def _select_all(self):
        """选择当前页所有图集"""
        if not self.title_groups:
            return
        
        # 计算当前页的索引范围
        start_idx = self.current_page * PAGE_SIZE
        end_idx = min(start_idx + PAGE_SIZE, len(self.title_groups))
        
        # 选择当前页的所有图集
        for idx in range(start_idx, end_idx):
            self.selected_groups[idx] = True
        
        # 更新显示
        self._update_album_list()
        
        # 启用下载按钮
        self.download_btn.config(state="normal")
    
    def _deselect_all(self):
        """取消选择当前页所有图集"""
        if not self.title_groups:
            return
        
        # 计算当前页的索引范围
        start_idx = self.current_page * PAGE_SIZE
        end_idx = min(start_idx + PAGE_SIZE, len(self.title_groups))
        
        # 取消选择当前页的所有图集
        for idx in range(start_idx, end_idx):
            self.selected_groups[idx] = False
        
        # 更新显示
        self._update_album_list()
        
        # 如果没有选中的图集，禁用下载按钮
        if not any(self.selected_groups.values()):
            self.download_btn.config(state="disabled")
    
    def _start_download(self):
        """开始下载选中图集"""
        if not any(self.selected_groups.values()):
            messagebox.showinfo("提示", "请先选择要下载的图集")
            return
        
        if self.is_downloading:
            messagebox.showinfo("提示", "当前已有下载任务在进行")
            return
        
        # 获取下载选项
        output_dir = self.output_dir_path.get()
        if not output_dir:
            messagebox.showerror("错误", "请先设置保存目录")
            return
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 配置代理
        proxies = None
        if self.use_proxy.get():
            proxy_address = self.proxy_address.get()
            if proxy_address:
                proxies = {
                    'http': proxy_address,
                    'https': proxy_address
                }
                self._log(f"使用代理: {proxy_address}")
        
        # 获取选中的图集
        selected_albums = []
        for idx, selected in self.selected_groups.items():
            if selected and 0 <= idx < len(self.title_groups):
                selected_albums.append((idx, self.title_groups[idx]))
        
        if not selected_albums:
            messagebox.showinfo("提示", "请先选择要下载的图集")
            return
        
        # 准备下载参数
        workers = self.workers_count.get()
        retries = self.retries_count.get()
        
        # 重置停止事件
        self.stop_event.clear()
        
        # 启动下载线程
        self.is_downloading = True
        self.download_thread = threading.Thread(
            target=self._download_worker,
            args=(selected_albums, output_dir, workers, retries, proxies),
            daemon=True
        )
        self.download_thread.start()
        
        # 更新UI状态
        self.download_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.parse_btn.config(state="disabled")
        
        # 启动进度更新定时器
        self.root.after(100, self._update_progress)
    
    def _stop_download(self):
        """停止下载"""
        if not self.is_downloading:
            return
        
        # 设置停止事件
        self.stop_event.set()
        self._log("正在停止下载，请稍候...")
        
        # 禁用停止按钮
        self.stop_btn.config(state="disabled")
    
    def _download_worker(self, selected_albums, output_dir, workers, retries, proxies):
        """下载工作线程"""
        try:
            total_albums = len(selected_albums)
            total_urls = sum(len(urls) for _, (_, urls) in selected_albums)
            self._log(f"开始下载 {total_albums} 个图集，共 {total_urls} 个链接")
            
            # 初始化进度条
            self.progress_bar["maximum"] = total_urls
            self.progress_bar["value"] = 0
            
            # 下载每个图集
            for album_idx, (title, urls) in selected_albums:
                if self.stop_event.is_set():
                    self._log("下载已停止")
                    break
                
                # 创建保存目录
                album_dir = os.path.join(output_dir, title)
                os.makedirs(album_dir, exist_ok=True)
                
                self._log(f"开始下载图集: {title} ({len(urls)} 个文件)")
                
                # 下载图集中的所有文件
                successful, failed = self._download_album(urls, album_dir, workers, retries, proxies)
                
                # 输出结果
                self._log(f"图集 '{title}' 下载完成! 成功: {successful}/{len(urls)}")
                if failed:
                    self._log(f"下载失败: {len(failed)}/{len(urls)}")
                    self._log("失败链接:")
                    for url in failed[:5]:  # 只显示前5个失败链接
                        self._log(f"  - {url}")
                    if len(failed) > 5:
                        self._log(f"  ... 还有 {len(failed) - 5} 个未显示")
            
            if not self.stop_event.is_set():
                self._log("所有图集下载完成!")
            
        except Exception as e:
            self._log(f"下载过程出错: {str(e)}")
        finally:
            # 恢复UI状态
            self.is_downloading = False
            self.download_btn.config(state="normal")
            self.stop_btn.config(state="disabled")
            self.parse_btn.config(state="normal")
    
    def _download_album(self, urls, save_dir, max_workers, max_retries, proxies):
        """下载单个图集中的所有文件"""
        successful = 0
        failed = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有下载任务
            future_to_url = {}
            for url in urls:
                if self.stop_event.is_set():
                    break
                future = executor.submit(
                    self._download_file, url, save_dir, max_retries, proxies
                )
                future_to_url[future] = url
            
            # 处理完成的任务
            for future in as_completed(future_to_url):
                if self.stop_event.is_set():
                    # 取消所有未完成的任务
                    for f in future_to_url:
                        if not f.done():
                            f.cancel()
                    break
                
                url = future_to_url[future]
                try:
                    success = future.result()
                    if success:
                        successful += 1
                    else:
                        failed.append(url)
                except Exception as e:
                    self._log(f"下载出现异常 {url}: {str(e)}")
                    failed.append(url)
                
                # 更新进度
                self.progress_bar["value"] += 1
        
        return successful, failed
    
    def _download_file(self, url, save_dir, max_retries, proxies):
        """下载单个文件"""
        for attempt in range(max_retries + 1):
            try:
                # 尝试下载
                response = requests.get(url, stream=True, timeout=30, proxies=proxies)
                response.raise_for_status()
                
                # 从URL中提取文件名
                filename = extract_filename_from_url(url)
                save_path = os.path.join(save_dir, filename)
                
                # 下载文件
                with open(save_path, 'wb') as file:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            file.write(chunk)
                
                return True  # 下载成功
                
            except Exception as e:
                if attempt < max_retries:
                    # 计算退避时间（指数退避）
                    wait_time = 2 ** attempt
                    time.sleep(wait_time)
                    continue
                else:
                    # 所有重试都失败
                    return False
    
    def _update_progress(self):
        """更新进度显示"""
        if self.is_downloading:
            # 更新进度标签
            current = self.progress_bar["value"]
            maximum = self.progress_bar["maximum"]
            percent = int(current / maximum * 100) if maximum > 0 else 0
            self.progress_label.config(text=f"下载进度: {current}/{maximum} ({percent}%)")
            
            # 继续定时更新
            self.root.after(100, self._update_progress)
    
    def _load_config(self):
        """加载配置"""
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r', encoding='utf-8') as file:
                    config = json.load(file)
                
                if "output_dir" in config:
                    self.output_dir_path.set(config["output_dir"])
                if "use_proxy" in config:
                    self.use_proxy.set(config["use_proxy"])
                if "proxy_address" in config:
                    self.proxy_address.set(config["proxy_address"])
                if "workers_count" in config:
                    self.workers_count.set(config["workers_count"])
                if "retries_count" in config:
                    self.retries_count.set(config["retries_count"])
                
                self._log("已加载配置")
        except Exception as e:
            self._log(f"加载配置出错: {str(e)}")
    
    def _save_config(self):
        """保存配置"""
        try:
            config = {
                "output_dir": self.output_dir_path.get(),
                "use_proxy": self.use_proxy.get(),
                "proxy_address": self.proxy_address.get(),
                "workers_count": self.workers_count.get(),
                "retries_count": self.retries_count.get()
            }
            
            with open(CONFIG_FILE, 'w', encoding='utf-8') as file:
                json.dump(config, file, ensure_ascii=False, indent=4)
            
            self._log("已保存配置")
        except Exception as e:
            self._log(f"保存配置出错: {str(e)}")
    
    def _log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.config(state="normal")
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.log_text.config(state="disabled")
    
    def _clear_log(self):
        """清除日志"""
        self.log_text.config(state="normal")
        self.log_text.delete("1.0", tk.END)
        self.log_text.config(state="disabled")
    
    def _on_closing(self):
        """应用程序关闭时的处理"""
        # 保存配置
        self._save_config()
        
        # 如果正在下载，询问是否确认退出
        if self.is_downloading:
            if messagebox.askokcancel("确认退出", "当前正在下载，确定要退出吗？"):
                self._stop_download()
                self.root.destroy()
        else:
            self.root.destroy()

# 主函数
def main():
    root = TkRoot()
    app = DownloaderApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()