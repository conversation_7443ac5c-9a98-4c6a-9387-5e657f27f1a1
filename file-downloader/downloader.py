import os
import requests
import argparse
import time
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm


def extract_filename_from_url(url):
    """从URL中提取文件名"""
    parsed_url = urlparse(url)
    path = parsed_url.path
    return os.path.basename(path)


def download_file_with_retry(url, save_dir, max_retries=3, proxies=None):
    """带重试机制的文件下载"""
    for attempt in range(max_retries):
        try:
            # 尝试下载
            response = requests.get(url, stream=True, timeout=30, proxies=proxies)
            response.raise_for_status()
            
            # 从URL中提取文件名
            filename = extract_filename_from_url(url)
            save_path = os.path.join(save_dir, filename)
            
            # 下载文件
            with open(save_path, 'wb') as file:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        file.write(chunk)
            
            return True  # 下载成功
            
        except Exception as e:
            if attempt < max_retries - 1:
                # 计算退避时间（指数退避）
                wait_time = 2 ** attempt
                time.sleep(wait_time)
                continue
            else:
                # 所有重试都失败
                print(f"下载失败 {url}: {str(e)}")
                return False


def download_all_files(urls, save_dir, max_workers=5, max_retries=3, proxies=None):
    """并行下载所有URL，带进度条和重试机制"""
    successful = 0
    failed = []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有下载任务
        future_to_url = {executor.submit(download_file_with_retry, url, save_dir, max_retries, proxies): url for url in urls}
        
        # 使用tqdm创建进度条
        with tqdm(total=len(urls), desc="下载进度") as progress_bar:
            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    success = future.result()
                    if success:
                        successful += 1
                    else:
                        failed.append(url)
                except Exception as e:
                    print(f"下载出现异常 {url}: {str(e)}")
                    failed.append(url)
                finally:
                    progress_bar.update(1)
    
    return successful, failed


def process_file(input_file, output_dir, max_workers=5, max_retries=3, proxies=None):
    """处理输入文件，识别多个标题组，并下载所有链接"""
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as file:
        lines = [line.strip() for line in file.readlines() if line.strip()]
    
    # 保存所有创建的目录
    created_dirs = []
    
    # 识别标题行（非URL行）和链接行（URL行）
    title_groups = []
    current_title = None
    current_urls = []
    
    for line in lines:
        # 如果行不是以http开头，则认为是标题行
        if not line.startswith('http'):
            # 如果已经有标题，则将当前标题和URL保存
            if current_title and current_urls:
                title_groups.append((current_title, current_urls))
            # 开始新的标题组
            current_title = line
            current_urls = []
        else:
            # 如果是URL行，添加到当前标题的URL列表
            current_urls.append(line)
    
    # 添加最后一个标题组
    if current_title and current_urls:
        title_groups.append((current_title, current_urls))
    
    # 处理每个标题组
    for title, urls in title_groups:
        # 创建以标题命名的子目录
        title_dir = os.path.join(output_dir, title)
        os.makedirs(title_dir, exist_ok=True)
        
        print(f"\n==== 处理标题组: {title} ====")
        print(f"找到 {len(urls)} 个文件链接")
        print(f"文件将保存至: {title_dir}")
        print(f"使用 {max_workers} 个并行下载线程，最大重试次数: {max_retries}")
        if proxies:
            print(f"使用代理: {proxies.get('http')}")
        
        # 下载当前标题组的所有URL
        successful, failed = download_all_files(urls, title_dir, max_workers, max_retries, proxies)
        
        print(f"\n标题组 '{title}' 下载完成! 成功: {successful}/{len(urls)}")
        if failed:
            print(f"下载失败: {len(failed)}/{len(urls)}")
            print("失败链接:")
            for url in failed[:10]:  # 只显示前10个失败链接
                print(f"  - {url}")
            if len(failed) > 10:
                print(f"  ... 还有 {len(failed) - 10} 个未显示")
        
        # 添加到已创建目录列表
        created_dirs.append(title_dir)
    
    return created_dirs


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="从文本文件下载链接")
    parser.add_argument("input_file", help="包含下载链接的输入文件路径")
    parser.add_argument("-o", "--output-dir", default="downloaded_files", 
                        help="文件下载保存的目录，默认为'downloaded_files'")
    parser.add_argument("-w", "--workers", type=int, default=5,
                        help="并行下载的线程数，默认为5")
    parser.add_argument("-r", "--retries", type=int, default=3,
                        help="下载失败时的最大重试次数，默认为3")
    parser.add_argument("-p", "--use-proxy", action="store_true",
                        help="是否使用代理，默认不使用")
    parser.add_argument("--proxy", default="http://localhost:1013",
                        help="代理服务器地址，默认为'http://localhost:1013'")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件 '{args.input_file}' 不存在")
        return
    
    # 配置代理
    proxies = None
    if args.use_proxy:
        proxies = {
            'http': args.proxy,
            'https': args.proxy
        }
        print(f"启用代理: {args.proxy}")
    
    print(f"开始处理文件: {args.input_file}")
    created_dirs = process_file(args.input_file, args.output_dir, args.workers, args.retries, proxies)
    
    if created_dirs:
        print("\n所有文件下载完成!")
        print(f"文件已保存到以下目录:")
        for dir_path in created_dirs:
            print(f"- {dir_path}")
    else:
        print("未找到有效的下载内容。")


if __name__ == "__main__":
    main() 