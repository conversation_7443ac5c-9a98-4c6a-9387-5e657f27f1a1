# 文件下载器

这是一个简单的Python工具，用于从文本文件中读取URL链接并将其下载到指定目录。

## 功能特点

- 从文本文件中读取链接（支持Telegram文件链接等任何HTTP链接）
- 自动提取第一行作为目录名
- 将下载的文件保存到以标题命名的子目录
- **并行下载**，显著提高下载速度
- **进度条显示**，直观展示下载进度
- **自动重试机制**，处理临时网络问题
- **代理支持**，解决网络访问限制问题
- 显示下载进度和成功/失败统计

## 安装

1. 确保已安装Python 3.6+
2. 安装所需依赖：

```bash
pip install -r requirements.txt
```

## 使用方法

基本用法：

```bash
python downloader.py 输入文件路径
```

高级选项：

```bash
python downloader.py 输入文件路径 -o 自定义输出目录 -w 并行下载数 -r 重试次数 -p --proxy 代理地址
```

参数说明：
- `-o, --output-dir`: 指定下载文件保存的目录，默认为`downloaded_files`
- `-w, --workers`: 指定并行下载的线程数，默认为5
- `-r, --retries`: 指定下载失败时的最大重试次数，默认为3
- `-p, --use-proxy`: 启用代理，不带此参数则不使用代理
- `--proxy`: 设置代理服务器地址，默认为`http://localhost:1013`

### 示例

```bash
# 基本用法
python downloader.py example_links.txt

# 指定输出目录
python downloader.py example_links.txt -o my_downloads

# 使用10个并行线程下载
python downloader.py example_links.txt -w 10

# 下载失败时最多重试5次
python downloader.py example_links.txt -r 5

# 使用默认代理（本机端口1013）
python downloader.py example_links.txt -p

# 使用自定义代理
python downloader.py example_links.txt -p --proxy http://custom-proxy:8080

# 组合使用
python downloader.py example_links.txt -o my_downloads -w 8 -r 4 -p
```

这将读取`example_links.txt`中的所有链接，并下载到指定目录中。

### 输入文件格式

输入文件的第一行通常是标题或描述，将用作保存目录的名称。
之后的每一行应该是一个URL链接。例如：

```
XiuRen秀人网-NO6919-柚琪Rich-04-06
https://telegra.ph/file/a93b04abaf5addc125a52.jpg
https://telegra.ph/file/9981d00e21d3d4ed896ac.jpg
```

## 注意事项

- 程序会自动跳过非URL格式的行
- 下载失败的文件会被记录并显示
- 并行下载可能会增加网络负载，如果遇到网站限制，请考虑减少并行数量
- 为避免请求过快被服务器拒绝，程序使用指数退避策略进行重试
- 当遇到网络访问限制时，可以使用代理功能绕过限制 