html,
body {
  margin: 0;
  padding: 0;
}

/*满天星*/

audio {
  z-index: 5;
  position: absolute;
  bottom: 0;
  opacity: 0.1;
  -webkit-transition: all 2s;
  -moz-transition: all 2s;
  -ms-transition: all 2s;
  -o-transition: all 2s;
  transition: all 2s;
}

audio:hover {
  opacity: 1;
}

.wall {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
/* 背景图 */
div#background {
  background: url("../assets/preview.jpg") no-repeat;
  -webkit-animation: dd 100s linear infinite;
  -moz-animation: dd 100s linear infinite;
  -o-animation: dd 100s linear infinite;
  animation: dd 100s linear infinite;
  background-size: cover;
}
/* 荧光点 */
div#midground {
  background: url("../assets/midground.png");
  /* z-index: 1; */
  -webkit-animation: cc 100s linear infinite;
  -moz-animation: cc 100s linear infinite;
  -o-animation: cc 100s linear infinite;
  animation: cc 100s linear infinite;
}

div#foreground {
  background: url("../assets/foreground.png");
  /* z-index: 2; */
  -webkit-animation: cc 153s linear infinite;
  -o-animation: cc 153s linear infinite;
  -moz-animation: cc 153s linear infinite;
  animation: cc 153s linear infinite;
}

div#top {
  background: url("../assets/midground.png");
  /* z-index: 4; */
  -webkit-animation: dd 100s linear infinite;
  -o-animation: dd 100s linear infinite;
  animation: da 100s linear infinite;
}

@-webkit-keyframes cc {
  from {
    background-position: 0 0;
    transform: translateY(10px);
  }
  to {
    background-position: 600% 0;
  }
}

@-o-keyframes cc {
  from {
    background-position: 0 0;
    transform: translateY(10px);
  }
  to {
    background-position: 600% 0;
  }
}

@-moz-keyframes cc {
  from {
    background-position: 0 0;
    transform: translateY(10px);
  }
  to {
    background-position: 600% 0;
  }
}

@keyframes cc {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 600% 0;
  }
}

@keyframes da {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 600%;
  }
}

@-webkit-keyframes da {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 600%;
  }
}

@-moz-keyframes da {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 600%;
  }
}

@-ms-keyframes da {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 600%;
  }
}
